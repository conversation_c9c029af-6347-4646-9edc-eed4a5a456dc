@echo off
REM Super-V5 Development Environment Setup Script for Windows
REM This script sets up the development environment using uv and Python 3.12

setlocal enabledelayedexpansion

echo 🚀 Super-V5 Development Environment Setup
echo ========================================
echo.

REM Check if uv is installed
where uv >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] uv is not installed. Please install uv first:
    echo   Visit: https://docs.astral.sh/uv/getting-started/installation/
    echo   Or run: powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
    pause
    exit /b 1
)

echo [SUCCESS] uv is installed
uv --version

REM Check Python 3.12 availability
echo [INFO] Checking Python 3.12 availability...
uv python list | findstr "3.12" >nul
if %errorlevel% neq 0 (
    echo [WARNING] Python 3.12 not found. Installing Python 3.12...
    uv python install 3.12
)
echo [SUCCESS] Python 3.12 is available

REM Create virtual environment
echo [INFO] Creating virtual environment with Python 3.12...
if exist ".venv" (
    echo [WARNING] Virtual environment already exists. Removing old one...
    rmdir /s /q .venv
)

uv venv --python=3.12
if %errorlevel% neq 0 (
    echo [ERROR] Failed to create virtual environment
    pause
    exit /b 1
)
echo [SUCCESS] Virtual environment created at .venv\

REM Install dependencies
echo [INFO] Installing dependencies...
uv sync
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies installed successfully

REM Setup environment file
if not exist ".env" (
    echo [INFO] Creating .env file from template...
    copy .env.example .env >nul
    echo [SUCCESS] .env file created
    echo [WARNING] Please edit .env file with your configuration before running the application
) else (
    echo [WARNING] .env file already exists, skipping...
)

echo.
echo 🎉 Setup completed successfully!
echo.
echo Next steps:
echo 1. Activate virtual environment:
echo    .venv\Scripts\activate
echo.
echo 2. Edit configuration:
echo    notepad .env
echo.
echo 3. Initialize database:
echo    make init-db
echo.
echo 4. Start development server:
echo    make run
echo.
echo 5. Access API documentation:
echo    http://localhost:8000/docs
echo.
echo For more commands, run: make help

pause
