#!/bin/bash

# Super-V5 Development Environment Setup Script
# This script sets up the development environment using uv and Python 3.12

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if uv is installed
check_uv() {
    if ! command_exists uv; then
        print_error "uv is not installed. Please install uv first:"
        echo "  curl -LsSf https://astral.sh/uv/install.sh | sh"
        echo "  or visit: https://docs.astral.sh/uv/getting-started/installation/"
        exit 1
    fi
    print_success "uv is installed: $(uv --version)"
}

# Check Python 3.12 availability
check_python() {
    if ! uv python list | grep -q "3.12"; then
        print_warning "Python 3.12 not found. Installing Python 3.12..."
        uv python install 3.12
    fi
    print_success "Python 3.12 is available"
}

# Create virtual environment
create_venv() {
    print_status "Creating virtual environment with Python 3.12..."
    
    if [ -d ".venv" ]; then
        print_warning "Virtual environment already exists. Removing old one..."
        rm -rf .venv
    fi
    
    uv venv --python=3.12
    print_success "Virtual environment created at .venv/"
}

# Install dependencies
install_deps() {
    print_status "Installing dependencies..."
    uv sync
    print_success "Dependencies installed successfully"
}

# Setup environment file
setup_env() {
    if [ ! -f ".env" ]; then
        print_status "Creating .env file from template..."
        cp .env.example .env
        print_success ".env file created"
        print_warning "Please edit .env file with your configuration before running the application"
    else
        print_warning ".env file already exists, skipping..."
    fi
}

# Setup pre-commit hooks
setup_precommit() {
    print_status "Setting up pre-commit hooks..."
    if [ -f ".venv/bin/activate" ]; then
        source .venv/bin/activate
        pre-commit install
        print_success "Pre-commit hooks installed"
    else
        print_warning "Virtual environment not found, skipping pre-commit setup"
    fi
}

# Main setup function
main() {
    echo "🚀 Super-V5 Development Environment Setup"
    echo "========================================"
    echo ""
    
    # Check prerequisites
    check_uv
    check_python
    
    # Setup environment
    create_venv
    install_deps
    setup_env
    setup_precommit
    
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Activate virtual environment:"
    echo "   source .venv/bin/activate"
    echo ""
    echo "2. Edit configuration:"
    echo "   nano .env"
    echo ""
    echo "3. Initialize database:"
    echo "   make init-db"
    echo ""
    echo "4. Start development server:"
    echo "   make run"
    echo ""
    echo "5. Access API documentation:"
    echo "   http://localhost:8000/docs"
    echo ""
    echo "For more commands, run: make help"
}

# Run main function
main "$@"
