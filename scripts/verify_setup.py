#!/usr/bin/env python3
"""
Setup Verification Script

This script verifies that the development environment is properly set up.
"""

import sys
import subprocess
import importlib
from pathlib import Path


def check_python_version():
    """Check Python version."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 12:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Need Python 3.12+")
        return False


def check_virtual_environment():
    """Check if running in virtual environment."""
    print("\n🏠 Checking virtual environment...")
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Running in virtual environment")
        print(f"   Virtual env path: {sys.prefix}")
        return True
    else:
        print("❌ Not running in virtual environment")
        print("   Please activate virtual environment: source .venv/bin/activate")
        return False


def check_required_packages():
    """Check if required packages are installed."""
    print("\n📦 Checking required packages...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'pydantic',
        'redis',
        'alembic',
        'pytest'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} - installed")
        except ImportError:
            print(f"❌ {package} - missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("   Run: make dev-install")
        return False
    
    return True


def check_environment_file():
    """Check if .env file exists."""
    print("\n⚙️  Checking environment configuration...")
    
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ .env file exists")
        return True
    elif env_example.exists():
        print("❌ .env file missing")
        print("   Run: cp .env.example .env")
        return False
    else:
        print("❌ Both .env and .env.example missing")
        return False


def check_database_connection():
    """Check database connection."""
    print("\n🗄️  Checking database connection...")
    
    try:
        from app.core.config import get_settings
        from app.infrastructure.database.session import check_database_health
        import asyncio
        
        settings = get_settings()
        print(f"   Database URL: {settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else 'configured'}")
        
        # Try to check database health
        try:
            result = asyncio.run(check_database_health())
            if result:
                print("✅ Database connection - OK")
                return True
            else:
                print("❌ Database connection - Failed")
                return False
        except Exception as e:
            print(f"⚠️  Database connection - Warning: {e}")
            print("   Database is required for full functionality")
            print("   To start PostgreSQL: docker run -d -p 5432:5432 -e POSTGRES_PASSWORD=password postgres:16")
            return True  # Don't fail verification for database in development
            
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False


def check_redis_connection():
    """Check Redis connection."""
    print("\n🔴 Checking Redis connection...")
    
    try:
        from app.core.config import get_settings
        from app.infrastructure.cache.redis_client import check_redis_health
        import asyncio
        
        settings = get_settings()
        print(f"   Redis URL: {settings.REDIS_URL}")
        
        try:
            result = asyncio.run(check_redis_health())
            if result:
                print("✅ Redis connection - OK")
                return True
            else:
                print("❌ Redis connection - Failed")
                return False
        except Exception as e:
            print(f"⚠️  Redis connection - Warning: {e}")
            print("   Redis is optional for basic functionality")
            print("   To start Redis: docker run -d -p 6379:6379 redis:7-alpine")
            return True  # Don't fail verification for Redis
            
    except Exception as e:
        print(f"❌ Redis check failed: {e}")
        return False


def check_application_startup():
    """Check if application can start."""
    print("\n🚀 Checking application startup...")
    
    try:
        from app.main import app
        print("✅ Application imports successfully")
        
        # Try to create FastAPI app
        if app:
            print("✅ FastAPI app created successfully")
            return True
        else:
            print("❌ FastAPI app creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Application startup failed: {e}")
        return False


def main():
    """Run all verification checks."""
    print("🔍 Super-V5 Setup Verification")
    print("=" * 40)
    
    checks = [
        check_python_version,
        check_virtual_environment,
        check_required_packages,
        check_environment_file,
        check_database_connection,
        check_redis_connection,
        check_application_startup
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        try:
            if check():
                passed += 1
        except Exception as e:
            print(f"❌ Check failed with error: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Verification Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! Your development environment is ready.")
        print("\nNext steps:")
        print("1. Start the development server: make run")
        print("2. Access API docs: http://localhost:8000/docs")
        print("3. Run tests: make test")
        return True
    else:
        print("❌ Some checks failed. Please fix the issues above.")
        print("\nCommon solutions:")
        print("- Activate virtual environment: source .venv/bin/activate")
        print("- Install dependencies: make dev-install")
        print("- Copy environment file: cp .env.example .env")
        print("- Start services: docker-compose up -d postgres redis")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
