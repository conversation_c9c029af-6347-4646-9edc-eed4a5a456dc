#!/usr/bin/env python3
"""
Database Seeding Script

This script seeds the database with initial data including
default agents, sample conversations, and configuration data.
"""

import asyncio
import logging
import sys
import uuid
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import get_settings
from app.core.constants import AgentType, AgentStatus
from app.infrastructure.database.session import get_db_session
from app.models.database.agent import Agent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def seed_agents():
    """Seed default agents."""
    logger.info("Seeding default agents...")
    
    async with get_db_session() as session:
        # Check if agents already exist
        existing_agents = await session.execute("SELECT COUNT(*) FROM agents")
        count = existing_agents.scalar()
        
        if count > 0:
            logger.info(f"Agents already exist ({count} found), skipping seeding")
            return
        
        # Create default agents
        agents = [
            Agent(
                id=uuid.uuid4(),
                name="V5 Default Agent",
                type=AgentType.V5_AGENT,
                version="1.0.0",
                description="Default V5 system agent for general-purpose tasks",
                config={
                    "max_iterations": 50,
                    "timeout": 300,
                    "temperature": 0.7,
                    "model": "gpt-4-turbo-preview"
                },
                status=AgentStatus.IDLE,
                capabilities=[
                    "conversation",
                    "question_answering", 
                    "task_execution",
                    "tool_usage"
                ],
                tools=[
                    "search",
                    "calculator",
                    "database_query"
                ],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),
            Agent(
                id=uuid.uuid4(),
                name="Deep Research Agent",
                type=AgentType.DEEP_RESEARCH,
                version="1.0.0",
                description="Specialized agent for deep research and analysis tasks",
                config={
                    "max_iterations": 100,
                    "timeout": 600,
                    "temperature": 0.3,
                    "model": "gpt-4-turbo-preview"
                },
                status=AgentStatus.IDLE,
                capabilities=[
                    "research",
                    "analysis",
                    "data_gathering",
                    "synthesis"
                ],
                tools=[
                    "search",
                    "database_query",
                    "web_scraper",
                    "document_analyzer"
                ],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),
            Agent(
                id=uuid.uuid4(),
                name="Task Planner Agent",
                type=AgentType.TASK_PLANNER,
                version="1.0.0",
                description="Agent specialized in task planning and decomposition",
                config={
                    "max_iterations": 30,
                    "timeout": 180,
                    "temperature": 0.5,
                    "model": "gpt-4-turbo-preview"
                },
                status=AgentStatus.IDLE,
                capabilities=[
                    "planning",
                    "task_decomposition",
                    "scheduling",
                    "optimization"
                ],
                tools=[
                    "calendar",
                    "project_manager",
                    "resource_planner"
                ],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        ]
        
        # Add agents to session
        for agent in agents:
            session.add(agent)
        
        # Commit changes
        await session.commit()
        
        logger.info(f"Successfully seeded {len(agents)} default agents")


async def seed_sample_data():
    """Seed sample data for development/testing."""
    settings = get_settings()
    
    # Only seed sample data in development environment
    if settings.ENVIRONMENT != "development":
        logger.info("Skipping sample data seeding (not in development environment)")
        return
    
    logger.info("Seeding sample data for development...")
    
    # TODO: Add sample conversations, users, etc.
    # This would include:
    # - Sample user accounts
    # - Sample conversations
    # - Sample messages
    # - Sample agent executions
    
    logger.info("Sample data seeding completed")


async def main():
    """Run database seeding."""
    try:
        settings = get_settings()
        logger.info(f"Seeding database for environment: {settings.ENVIRONMENT}")
        
        # Seed default agents
        await seed_agents()
        
        # Seed sample data for development
        await seed_sample_data()
        
        logger.info("Database seeding completed successfully")
        
    except Exception as e:
        logger.error(f"Database seeding failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
