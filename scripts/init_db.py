#!/usr/bin/env python3
"""
Database Initialization Script

This script initializes the database by creating all tables
and setting up the initial schema.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import get_settings
from app.infrastructure.database.session import init_db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Initialize the database."""
    try:
        settings = get_settings()
        logger.info(f"Initializing database for environment: {settings.ENVIRONMENT}")
        
        # Initialize database
        await init_db()
        
        logger.info("Database initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
