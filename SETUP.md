# Super-V5 开发环境设置指南

本文档详细说明如何使用 `uv` 和 Python 3.12 设置 Super-V5 开发环境。

## 📋 前置要求

### 1. 安装 uv

`uv` 是一个极快的 Python 包管理器，用 Rust 编写。

#### Linux/macOS
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### Windows
```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

#### 验证安装
```bash
uv --version
```

### 2. Python 3.12

uv 会自动管理 Python 版本，无需手动安装 Python 3.12。

## 🚀 快速设置

### 方法 1: 自动化脚本（推荐）

```bash
# 运行自动化设置脚本
make setup

# 或者直接运行脚本
./scripts/setup.sh        # Linux/macOS
scripts\setup.bat          # Windows
```

### 方法 2: 手动设置

```bash
# 1. 创建虚拟环境
make venv-create

# 2. 安装依赖
make dev-install

# 3. 设置环境文件
cp .env.example .env

# 4. 编辑配置
nano .env

# 5. 初始化数据库
make init-db

# 6. 启动开发服务器
make run
```

## 🔧 详细设置步骤

### 1. 创建虚拟环境

```bash
# 使用 Python 3.12 创建虚拟环境
uv venv --python=3.12

# 或使用 Makefile
make venv-create
```

这将在项目根目录创建 `.venv` 文件夹。

### 2. 激活虚拟环境

#### Linux/macOS
```bash
source .venv/bin/activate
```

#### Windows
```cmd
.venv\Scripts\activate
```

### 3. 安装依赖

```bash
# 安装所有依赖（包括开发依赖）
uv sync

# 或仅安装生产依赖
uv sync --no-dev

# 使用 Makefile
make dev-install    # 开发依赖
make install        # 生产依赖
```

### 4. 环境配置

```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

#### 关键配置项

```bash
# 应用配置
APP_NAME=super-v5
DEBUG=true
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/super_v5

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# LLM 配置
OPENAI_API_KEY=your-openai-api-key-here

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
```

### 5. 数据库设置

```bash
# 初始化数据库
python scripts/init_db.py

# 运行迁移
alembic upgrade head

# 填充初始数据
python scripts/seed_data.py

# 或使用 Makefile 一键完成
make init-db
```

### 6. 启动应用

```bash
# 开发模式启动
uvicorn app.main:app --reload

# 或使用 Makefile
make run
```

应用将在 http://localhost:8000 启动。

## 📚 常用命令

### 虚拟环境管理

```bash
# 创建虚拟环境
make venv-create

# 查看虚拟环境信息
make venv-info

# 删除虚拟环境
make venv-remove
```

### 依赖管理

```bash
# 安装新包
uv add package-name

# 安装开发依赖
uv add --dev package-name

# 移除包
uv remove package-name

# 更新所有依赖
uv sync --upgrade

# 查看依赖树
uv tree
```

### 开发工具

```bash
# 运行测试
make test

# 代码格式化
make format

# 代码检查
make lint

# 清理缓存
make clean
```

### 数据库操作

```bash
# 创建新迁移
alembic revision --autogenerate -m "description"

# 运行迁移
make migrate

# 填充数据
make seed

# 完整数据库初始化
make init-db
```

## 🐳 Docker 开发

如果您更喜欢使用 Docker：

```bash
# 构建镜像
make docker-build

# 运行容器
make docker-run

# 使用 Docker Compose
docker-compose up -d
```

## 🔍 故障排除

### 常见问题

#### 1. uv 命令未找到
```bash
# 确保 uv 已正确安装并在 PATH 中
which uv
uv --version

# 重新安装 uv
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### 2. Python 3.12 未找到
```bash
# uv 会自动安装 Python 3.12
uv python install 3.12

# 查看可用的 Python 版本
uv python list
```

#### 3. 虚拟环境激活失败
```bash
# 确保虚拟环境存在
ls -la .venv/

# 重新创建虚拟环境
make venv-remove
make venv-create
```

#### 4. 依赖安装失败
```bash
# 清理缓存并重新安装
uv cache clean
uv sync --reinstall
```

#### 5. 数据库连接失败
```bash
# 检查数据库服务是否运行
pg_isready -h localhost -p 5432

# 检查配置
cat .env | grep DATABASE_URL
```

### 性能优化

#### 1. 使用 uv 缓存
```bash
# 查看缓存状态
uv cache info

# 清理缓存（如果需要）
uv cache clean
```

#### 2. 并行安装
```bash
# uv 默认使用并行安装，比 pip 快 10-100 倍
uv sync --no-cache  # 禁用缓存进行测试
```

## 📖 更多资源

- [uv 官方文档](https://docs.astral.sh/uv/)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [LangGraph 文档](https://python.langchain.com/docs/langgraph)
- [项目 API 文档](http://localhost:8000/docs)

## 🤝 开发工作流

### 日常开发

1. **激活环境**
   ```bash
   source .venv/bin/activate
   ```

2. **拉取最新代码**
   ```bash
   git pull origin main
   ```

3. **更新依赖**
   ```bash
   uv sync
   ```

4. **运行测试**
   ```bash
   make test
   ```

5. **启动开发服务器**
   ```bash
   make run
   ```

### 添加新功能

1. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **添加依赖（如需要）**
   ```bash
   uv add new-package
   ```

3. **开发和测试**
   ```bash
   make test
   make lint
   ```

4. **提交代码**
   ```bash
   git add .
   git commit -m "Add new feature"
   git push origin feature/new-feature
   ```

### 部署准备

1. **运行完整测试套件**
   ```bash
   make test-all
   ```

2. **构建生产镜像**
   ```bash
   make docker-build
   ```

3. **验证构建**
   ```bash
   make docker-run
   ```

这样，您就可以高效地使用 uv 和 Python 3.12 进行 Super-V5 项目的开发了！
