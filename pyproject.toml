[project]
name = "super-v5"
version = "0.1.0"
description = "Super-V5 Enterprise LLM Application Backend Service Platform"
authors = [
    {name = "Super-V5 Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.12"
keywords = ["llm", "agent", "fastapi", "langgraph", "enterprise"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # Web Framework
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.30.0",
    # Agent Framework
    "langgraph>=0.6.2",
    "langchain>=0.3.27",
    "langchain-community>=0.3.27",
    "langchain-core>=0.3.27",
    # Data Validation & Serialization
    "pydantic>=2.11.7",
    "pydantic-settings>=2.6.0",
    # Database
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "asyncpg>=0.29.0", # PostgreSQL async driver
    # Cache
    "redis>=5.0.0",
    "hiredis>=2.3.0", # Redis performance boost
    # HTTP Client
    "httpx>=0.27.0",
    "aiohttp>=3.10.0",
    # Authentication & Security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.9",
    # Configuration Management
    "python-dotenv>=1.0.0",
    # Logging & Monitoring
    "structlog>=24.1.0",
    "prometheus-client>=0.20.0",
    # Utilities
    "python-dateutil>=2.9.0",
    "pytz>=2024.1",
    "click>=8.1.0",
    # LLM Integration
    "litellm>=1.50.0",
    "openai>=1.50.0",
    # Message Queue
    "aiokafka>=0.11.0",
    "greenlet>=3.2.3",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.27.0",  # For testing FastAPI
    
    # Code Quality
    "black>=24.0.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
    
    # Documentation
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.5.0",
    
    # Development Tools
    "ipython>=8.20.0",
    "jupyter>=1.0.0",
]

production = [
    "gunicorn>=21.2.0",
    "gevent>=24.2.0",
]

[project.urls]
Homepage = "https://github.com/super-v5/super-v5"
Documentation = "https://docs.super-v5.com"
Repository = "https://github.com/super-v5/super-v5"
Issues = "https://github.com/super-v5/super-v5/issues"

[project.scripts]
super-v5 = "app.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]
dev-dependencies = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.12.0",
    "black>=24.0.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "langgraph.*",
    "langchain.*",
    "redis.*",
    "sqlalchemy.*",
    "alembic.*",
    "prometheus_client.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/scripts/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
