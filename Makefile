.PHONY: help setup venv-create venv-remove venv-info install dev-install test lint format clean run docker-build docker-run migrate seed

# Default target
help:
	@echo "Available commands:"
	@echo "  setup        - Run automated setup script"
	@echo "  venv-create  - Create virtual environment with Python 3.12"
	@echo "  venv-remove  - Remove virtual environment"
	@echo "  venv-info    - Show virtual environment information"
	@echo "  install      - Install production dependencies using uv"
	@echo "  dev-install  - Install development dependencies using uv"
	@echo "  test         - Run tests with pytest"
	@echo "  lint         - Run linting (flake8, mypy)"
	@echo "  format       - Format code (black, isort)"
	@echo "  clean        - Clean up cache and temporary files"
	@echo "  run          - Run the application in development mode"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run application in Docker container"
	@echo "  migrate      - Run database migrations"
	@echo "  seed         - Seed database with initial data"
	@echo "  init-db      - Initialize database (migrate + seed)"
	@echo "  setup-dev    - Complete development environment setup"
	@echo "  setup-quick  - Quick setup (using existing venv)"
	@echo "  verify       - Verify development environment setup"

# Automated Setup
setup:
	@echo "Running automated setup script..."
	@if [ -f "scripts/setup.sh" ]; then \
		chmod +x scripts/setup.sh && ./scripts/setup.sh; \
	elif [ -f "scripts/setup.bat" ]; then \
		scripts/setup.bat; \
	else \
		echo "Setup script not found. Running manual setup..."; \
		$(MAKE) setup-dev; \
	fi

# Virtual Environment Management
venv-create:
	@echo "Creating virtual environment with Python 3.12..."
	uv venv --python=3.12
	@echo "Virtual environment created successfully!"
	@echo "To activate: source .venv/bin/activate (Linux/Mac) or .venv\\Scripts\\activate (Windows)"

venv-remove:
	@echo "Removing virtual environment..."
	rm -rf .venv
	@echo "Virtual environment removed!"

venv-info:
	@echo "Virtual environment information:"
	@if [ -d ".venv" ]; then \
		echo "✓ Virtual environment exists at: .venv"; \
		echo "Python version:"; \
		.venv/bin/python --version 2>/dev/null || echo "Virtual environment not activated"; \
	else \
		echo "✗ No virtual environment found. Run 'make venv-create' to create one."; \
	fi

# Installation
install:
	@echo "Installing production dependencies..."
	uv sync --no-dev

dev-install:
	@echo "Installing development dependencies..."
	uv sync

# Development
run:
	uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

run-prod:
	uv run gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Testing
test:
	uv run pytest

test-cov:
	uv run pytest --cov=app --cov-report=html --cov-report=term

test-unit:
	uv run pytest tests/unit/

test-integration:
	uv run pytest tests/integration/

# Code Quality
lint:
	uv run flake8 app tests
	uv run mypy app

format:
	uv run black app tests scripts
	uv run isort app tests scripts

format-check:
	uv run black --check app tests scripts
	uv run isort --check-only app tests scripts

# Database
migrate:
	uv run alembic upgrade head

migrate-create:
	uv run alembic revision --autogenerate -m "$(name)"

migrate-downgrade:
	uv run alembic downgrade -1

seed:
	uv run python scripts/seed_data.py

init-db:
	uv run python scripts/init_db.py
	$(MAKE) migrate
	$(MAKE) seed

# Docker
docker-build:
	docker build -f docker/Dockerfile -t super-v5:latest .

docker-run:
	docker run -p 8000:8000 --env-file .env super-v5:latest

docker-compose-up:
	docker-compose up -d

docker-compose-down:
	docker-compose down

# Cleanup
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf htmlcov/
	rm -rf .coverage

# Pre-commit hooks
pre-commit-install:
	uv run pre-commit install

pre-commit-run:
	uv run pre-commit run --all-files

# Documentation
docs-serve:
	uv run mkdocs serve

docs-build:
	uv run mkdocs build

# Security
security-check:
	uv run bandit -r app/

# Performance
profile:
	uv run python -m cProfile -o profile.stats scripts/profile_app.py

# Environment setup
setup-dev: venv-create dev-install pre-commit-install
	@echo "Development environment setup complete!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Activate virtual environment: source .venv/bin/activate"
	@echo "2. Copy environment file: cp .env.example .env"
	@echo "3. Edit .env with your configuration"
	@echo "4. Initialize database: make init-db"
	@echo "5. Start development server: make run"

setup-quick: venv-info dev-install
	@echo "Quick development setup complete (using existing venv)!"

# Verification
verify:
	@echo "Verifying development environment setup..."
	python scripts/verify_setup.py

# CI/CD helpers
ci-test: format-check lint test

# Health check
health:
	curl -f http://localhost:8000/api/v1/health || exit 1
