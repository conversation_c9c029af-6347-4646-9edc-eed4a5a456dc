"""
Pytest Configuration

This module contains pytest fixtures and configuration
for the test suite.
"""

import asyncio
import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.infrastructure.database.session import get_db_session


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def async_client():
    """Create an async HTTP client for testing."""
    async with Async<PERSON>lient(app=app, base_url="http://test") as client:
        yield client


@pytest_asyncio.fixture
async def db_session():
    """Create a database session for testing."""
    async with get_db_session() as session:
        yield session


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User"
    }


@pytest.fixture
def sample_conversation_data():
    """Sample conversation data for testing."""
    return {
        "title": "Test Conversation",
        "agent_type": "v5_agent",
        "agent_config": {
            "temperature": 0.7,
            "max_tokens": 1000
        }
    }


@pytest.fixture
def sample_message_data():
    """Sample message data for testing."""
    return {
        "content": "Hello, this is a test message",
        "message_type": "user"
    }
