# Super-V5 大模型应用后端服务

<div align="center">
  
[![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115.12+-green.svg)](https://fastapi.tiangolo.com/)
[![LangGraph](https://img.shields.io/badge/LangGraph-0.6.0+-purple.svg)](https://python.langchain.com/docs/langgraph)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![CI/CD](https://github.com/super-v5/super-v5/workflows/CI/badge.svg)](https://github.com/super-v5/super-v5/actions)

一个基于FastAPI和LangGraph构建的生产级大模型应用后端服务，采用领域驱动设计(DDD)和清洁架构理念。

[🚀 快速开始](#快速开始) • [📖 文档](#文档) • [🛠️ 部署](#部署) • [🤝 贡献](#贡献)

</div>

## ✨ 主要特性

### 🎯 核心能力
- **多Agent架构** - 支持客服、数据分析、任务规划等多种业务Agent
- **工作流编排** - 基于LangGraph的状态机工作流管理
- **工具生态** - 丰富的内置工具和可扩展的工具注册机制
- **实时对话** - 支持流式响应和会话状态管理

### 🏗️ 架构设计
- **分层架构** - API层、服务层、领域层、基础设施层清晰分离
- **事件驱动** - 异步处理和事件驱动的Agent执行流程
- **微服务就绪** - 模块化设计，支持独立部署和扩展
- **云原生** - 容器化部署，支持Kubernetes编排

### 🔧 技术栈
- **后端框架**: FastAPI 0.115.12
- **Agent框架**: LangGraph 0.6.0
- **数据验证**: Pydantic 2.11.7
- **数据库**: PostgreSQL 16+ / Redis 7.0+
- **监控**: Prometheus + Grafana
- **部署**: Docker + Kubernetes

### 🛡️ 生产特性
- **高可用** - 主从复制、负载均衡、故障转移
- **安全** - JWT认证、输入验证、限流保护
- **监控** - 全链路监控、指标收集、告警通知
- **性能** - 多级缓存、连接池、并发优化

## 🚀 快速开始

### 环境要求

- Python 3.12+
- Docker & Docker Compose
- PostgreSQL 16+
- Redis 7.0+

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/super-v5/super-v5.git
cd super-v5
```

2. **安装依赖**
```bash
# 使用Poetry管理依赖
pip install poetry
poetry install

# 或使用pip
pip install -r requirements.txt
```

3. **配置环境**
```bash
# 复制环境配置
cp config/environments/.env.example .env

# 编辑配置文件
vim .env
```

4. **启动服务**
```bash
# 使用Docker Compose启动依赖服务
docker-compose up -d postgres redis

# 启动应用
poetry run uvicorn app.main:app --reload
```

5. **验证安装**
```bash
# 访问API文档
open http://localhost:8000/docs

# 健康检查
curl http://localhost:8000/api/health
```

### Docker部署

```bash
# 构建镜像
docker build -t super-v5:latest .

# 启动完整服务栈
docker-compose up -d

# 查看服务状态
docker-compose ps
```

## 📚 API使用示例

### 执行Agent

```python
import httpx
import asyncio

async def execute_customer_service_agent():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/agents/customer_service/execute",
            json={
                "messages": [
                    {
                        "role": "user",
                        "content": "我的订单什么时候能到？订单号是123456"
                    }
                ],
                "context": {
                    "user_id": "user_123",
                    "channel": "web"
                },
                "session_id": "session_456"
            },
            headers={"Authorization": "Bearer your_token_here"}
        )
        
        result = response.json()
        print(f"Agent响应: {result['data']['result']['final_answer']}")

# 运行示例
asyncio.run(execute_customer_service_agent())
```

### 流式响应

```python
async def stream_agent_response():
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            "http://localhost:8000/api/v1/agents/customer_service/execute",
            json={
                "messages": [{"role": "user", "content": "你好"}],
                "stream": True
            }
        ) as response:
            async for chunk in response.aiter_text():
                if chunk.startswith("data: "):
                    data = json.loads(chunk[6:])
                    print(f"流式数据: {data}")
```

### 获取Agent列表

```bash
curl -X GET "http://localhost:8000/api/v1/agents" \
  -H "Authorization: Bearer your_token_here"
```

## 🏗️ 项目结构

```
super-v5/
├── app/                           # 应用主目录
│   ├── api/                       # API层
│   │   ├── v1/endpoints/         # API端点
│   │   └── middleware/           # 中间件
│   ├── agents/                   # Agent业务层
│   │   ├── base/                # Agent基础框架
│   │   ├── implementations/     # 具体Agent实现
│   │   └── tools/               # Agent工具集
│   ├── services/                # 业务服务层
│   ├── models/                  # 数据模型
│   ├── infrastructure/          # 基础设施层
│   └── utils/                   # 工具函数
├── tests/                       # 测试目录
├── scripts/                     # 脚本目录
├── config/                      # 配置文件
├── docker/                      # Docker配置
├── k8s/                        # Kubernetes配置
└── docs/                       # 文档
```

## 🤖 Agent类型

### 客服Agent (customer_service)
处理客户咨询、订单查询、问题解答
```python
# 使用示例
{
    "agent_type": "customer_service",
    "messages": [{"role": "user", "content": "查询订单状态"}],
    "context": {"order_id": "123456"}
}
```

### 数据分析Agent (data_analyst)
数据查询、报表生成、趋势分析
```python
# 使用示例
{
    "agent_type": "data_analyst", 
    "messages": [{"role": "user", "content": "分析最近一周的销售数据"}],
    "context": {"date_range": "2024-01-01,2024-01-07"}
}
```

### 任务规划Agent (task_planner)
任务分解、进度跟踪、资源分配
```python
# 使用示例
{
    "agent_type": "task_planner",
    "messages": [{"role": "user", "content": "制定项目开发计划"}],
    "context": {"project": "super-v5", "deadline": "2024-06-01"}
}
```

## 🛠️ 工具系统

### 内置工具

| 工具名称 | 功能描述 | 分类 |
|---------|---------|------|
| elasticsearch_search | 全文搜索 | 搜索工具 |
| vector_search | 语义搜索 | 搜索工具 |
| database_query | 数据库查询 | 数据处理 |
| http_request | HTTP请求 | API集成 |
| sentiment_analysis | 情感分析 | AI增强 |
| text_summarization | 文本摘要 | AI增强 |

### 自定义工具

```python
from app.agents.tools.base import BaseTool, ToolConfig, ToolCategory
from app.agents.tools.registry import ToolRegistry

@ToolRegistry.register("my_custom_tool")
class MyCustomTool(BaseTool):
    def __init__(self):
        config = ToolConfig(
            name="my_custom_tool",
            description="我的自定义工具",
            category=ToolCategory.CUSTOM,
            timeout=30
        )
        super().__init__(config)
    
    async def _execute_core(self, input_data):
        # 实现工具逻辑
        return {"result": "success"}
```

## 📖 文档

### 设计文档
- [📋 Super-V5设计文档 v2.0](Super-V5设计文档_v2.0.md) - 完整的系统设计文档
- [🔧 工具系统详细设计](工具系统详细设计.md) - 工具系统架构和实现
- [🗄️ 数据库设计详细文档](数据库设计详细文档.md) - 数据库设计和优化
- [🚀 部署运维详细文档](部署运维详细文档.md) - 部署和运维指南

### API文档
- [Swagger UI](http://localhost:8000/docs) - 交互式API文档
- [ReDoc](http://localhost:8000/redoc) - 美观的API文档

### 开发指南
```bash
# 生成API文档
poetry run python scripts/generate_docs.py

# 运行测试
poetry run pytest tests/ -v

# 代码格式化
poetry run black app/ tests/
poetry run isort app/ tests/

# 类型检查
poetry run mypy app/

# 安全扫描
poetry run bandit -r app/
```

## 🚀 部署

### 开发环境
```bash
# 启动开发服务
docker-compose -f docker-compose.dev.yml up -d
```

### 测试环境
```bash
# 部署到测试环境
./scripts/deploy.sh --environment staging --image-tag develop
```

### 生产环境
```bash
# Kubernetes部署
kubectl apply -f k8s/
./scripts/deploy.sh --environment production --image-tag v2.0.0

# 健康检查
./scripts/ops.sh health

# 监控检查
open http://grafana.super-v5.com
```

## 📊 监控面板

### 应用监控
- **Grafana Dashboard**: http://grafana.super-v5.com
- **Prometheus Metrics**: http://prometheus.super-v5.com
- **健康检查**: http://api.super-v5.com/api/health

### 关键指标
- **QPS**: 每秒请求数
- **响应时间**: P95响应时间 < 2s
- **错误率**: 错误率 < 1%
- **可用性**: 可用性 > 99.9%

## 🔒 安全

### 认证授权
- JWT Token认证
- RBAC权限控制
- OAuth2集成支持

### 安全措施
- 输入验证和清洗
- SQL注入防护
- XSS攻击防护
- 请求限流
- 数据加密

### 安全配置
```python
# JWT配置
JWT_SECRET_KEY = "your-secret-key"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_MINUTES = 30

# 限流配置
RATE_LIMIT_REQUESTS = 100
RATE_LIMIT_WINDOW = 60
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
poetry run pytest tests/unit/ -v

# 集成测试
poetry run pytest tests/integration/ -v

# 端到端测试
poetry run pytest tests/e2e/ -v

# 覆盖率测试
poetry run pytest --cov=app --cov-report=html
```

### 性能测试
```bash
# 负载测试
./scripts/ops.sh load-test http://localhost:8000 10 1000

# 压力测试
k6 run tests/performance/load-test.js
```

## 🤝 贡献

我们欢迎所有形式的贡献！

### 贡献流程
1. Fork项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

### 开发规范
- 遵循 [PEP 8](https://pep8.org/) 代码规范
- 编写单元测试
- 更新文档
- 通过CI/CD检查

### 代码质量
```bash
# 代码检查
poetry run pre-commit run --all-files

# 安全扫描
poetry run safety check

# 依赖检查
poetry run pip-audit
```

## 📄 许可证

本项目采用 [MIT许可证](LICENSE) - 查看LICENSE文件了解详情。

## 🙏 致谢

感谢以下开源项目和贡献者：

- [FastAPI](https://fastapi.tiangolo.com/) - 高性能Web框架
- [LangChain](https://python.langchain.com/) - LLM应用开发框架
- [LangGraph](https://python.langchain.com/docs/langgraph) - 状态机工作流框架
- [Pydantic](https://pydantic-docs.helpmanual.io/) - 数据验证库

## 📞 联系我们

- **项目主页**: https://github.com/super-v5/super-v5
- **问题反馈**: https://github.com/super-v5/super-v5/issues
- **功能建议**: https://github.com/super-v5/super-v5/discussions
- **邮箱**: <EMAIL>

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个Star！**

Made with ❤️ by Super-V5 Team

</div>