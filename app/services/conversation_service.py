"""
Conversation Service

This module contains business logic for conversation management.
"""

import uuid
from datetime import datetime
from typing import List, Op<PERSON>, Tuple

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import ValidationException, DatabaseException
from app.infrastructure.database.repositories.conversation_repository import ConversationRepository
from app.models.domain.entities import ConversationEntity, MessageEntity
from app.models.schemas.conversation import (
    ConversationCreate,
    ConversationUpdate,
    MessageCreate
)
from app.models.schemas.request import PaginationRequest


class ConversationService:
    """
    Service for managing conversations and messages.
    
    Provides high-level business operations for conversation management,
    including creation, updates, message handling, and agent integration.
    """
    
    def __init__(self, db_session: AsyncSession):
        """
        Initialize conversation service.
        
        Args:
            db_session: Database session
        """
        self.db_session = db_session
        self.conversation_repository = ConversationRepository(db_session)
    
    async def create_conversation(
        self,
        conversation_data: ConversationCreate,
        user_id: str
    ) -> ConversationEntity:
        """
        Create a new conversation.
        
        Args:
            conversation_data: Conversation creation data
            user_id: User ID creating the conversation
            
        Returns:
            ConversationEntity: Created conversation
            
        Raises:
            ValidationException: If conversation data is invalid
            DatabaseException: If database operation fails
        """
        try:
            # Create conversation entity
            conversation = ConversationEntity(
                id=uuid.uuid4(),
                title=conversation_data.title,
                status="active",
                user_id=user_id,
                agent_type=conversation_data.agent_type,
                agent_config=conversation_data.agent_config or {},
                metadata=conversation_data.metadata or {},
                settings=conversation_data.settings or {},
                message_count=0,
                total_tokens=0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                last_activity_at=datetime.utcnow()
            )
            
            # Save to database
            saved_conversation = await self.conversation_repository.create(conversation)
            
            # Add initial message if provided
            if conversation_data.initial_message:
                initial_message = MessageCreate(
                    content=conversation_data.initial_message,
                    message_type="user"
                )
                await self.add_message(saved_conversation.id, initial_message, user_id)
            
            return saved_conversation
            
        except Exception as e:
            raise DatabaseException(f"Failed to create conversation: {str(e)}")
    
    async def get_conversation(
        self,
        conversation_id: uuid.UUID,
        user_id: Optional[str] = None
    ) -> Optional[ConversationEntity]:
        """
        Get conversation by ID.
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID for access control
            
        Returns:
            Optional[ConversationEntity]: Conversation or None if not found
        """
        try:
            conversation = await self.conversation_repository.get_by_id(conversation_id)
            
            # Check access permissions
            if conversation and user_id and conversation.user_id != user_id:
                return None
            
            return conversation
            
        except Exception as e:
            raise DatabaseException(f"Failed to get conversation: {str(e)}")
    
    async def list_conversations(
        self,
        user_id: str,
        pagination: PaginationRequest
    ) -> Tuple[List[ConversationEntity], int]:
        """
        List conversations for a user with pagination.
        
        Args:
            user_id: User ID
            pagination: Pagination parameters
            
        Returns:
            Tuple[List[ConversationEntity], int]: Conversations and total count
        """
        try:
            return await self.conversation_repository.list_by_user(
                user_id=user_id,
                page=pagination.page,
                page_size=pagination.page_size,
                sort_by=pagination.sort_by,
                sort_order=pagination.sort_order
            )
            
        except Exception as e:
            raise DatabaseException(f"Failed to list conversations: {str(e)}")
    
    async def update_conversation(
        self,
        conversation_id: uuid.UUID,
        conversation_data: ConversationUpdate,
        user_id: str
    ) -> Optional[ConversationEntity]:
        """
        Update conversation.
        
        Args:
            conversation_id: Conversation ID
            conversation_data: Update data
            user_id: User ID for access control
            
        Returns:
            Optional[ConversationEntity]: Updated conversation or None if not found
        """
        try:
            # Get existing conversation
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return None
            
            # Check if user can edit
            if not conversation.can_edit(user_id):
                raise ValidationException("Cannot edit this conversation")
            
            # Update fields
            update_data = conversation_data.model_dump(exclude_unset=True)
            conversation.update_from_dict(update_data)
            conversation.updated_at = datetime.utcnow()
            
            # Save changes
            return await self.conversation_repository.update(conversation)
            
        except ValidationException:
            raise
        except Exception as e:
            raise DatabaseException(f"Failed to update conversation: {str(e)}")
    
    async def delete_conversation(
        self,
        conversation_id: uuid.UUID,
        user_id: str
    ) -> bool:
        """
        Delete conversation (soft delete).
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID for access control
            
        Returns:
            bool: True if deleted, False if not found
        """
        try:
            # Get existing conversation
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                return False
            
            # Check if user can delete
            if not conversation.can_delete(user_id):
                raise ValidationException("Cannot delete this conversation")
            
            # Soft delete
            conversation.soft_delete()
            conversation.updated_at = datetime.utcnow()
            
            # Save changes
            await self.conversation_repository.update(conversation)
            return True
            
        except ValidationException:
            raise
        except Exception as e:
            raise DatabaseException(f"Failed to delete conversation: {str(e)}")
    
    async def add_message(
        self,
        conversation_id: uuid.UUID,
        message_data: MessageCreate,
        user_id: str
    ) -> MessageEntity:
        """
        Add message to conversation.
        
        Args:
            conversation_id: Conversation ID
            message_data: Message creation data
            user_id: User ID for access control
            
        Returns:
            MessageEntity: Created message
        """
        try:
            # Get conversation
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValidationException("Conversation not found")
            
            # Check if messages can be added
            if not conversation.can_add_message():
                raise ValidationException("Cannot add message to this conversation")
            
            # Create message entity
            message = MessageEntity(
                id=uuid.uuid4(),
                conversation_id=conversation_id,
                message_type=message_data.message_type,
                content=message_data.content,
                metadata=message_data.metadata or {},
                sequence_number=conversation.message_count + 1,
                parent_message_id=message_data.parent_message_id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Save message
            # TODO: Implement message repository
            # saved_message = await self.message_repository.create(message)
            
            # Update conversation
            conversation.add_message(message)
            await self.conversation_repository.update(conversation)
            
            return message
            
        except ValidationException:
            raise
        except Exception as e:
            raise DatabaseException(f"Failed to add message: {str(e)}")
    
    async def get_messages(
        self,
        conversation_id: uuid.UUID,
        user_id: str,
        pagination: PaginationRequest
    ) -> Tuple[List[MessageEntity], int]:
        """
        Get messages for a conversation with pagination.
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID for access control
            pagination: Pagination parameters
            
        Returns:
            Tuple[List[MessageEntity], int]: Messages and total count
        """
        try:
            # Check conversation access
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ValidationException("Conversation not found")
            
            # TODO: Implement message repository
            # return await self.message_repository.list_by_conversation(
            #     conversation_id=conversation_id,
            #     page=pagination.page,
            #     page_size=pagination.page_size,
            #     sort_by=pagination.sort_by or "sequence_number",
            #     sort_order=pagination.sort_order
            # )
            
            # Placeholder return
            return [], 0
            
        except ValidationException:
            raise
        except Exception as e:
            raise DatabaseException(f"Failed to get messages: {str(e)}")
