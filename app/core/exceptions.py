"""
Custom Exception Classes

This module defines all custom exceptions used throughout the application.
Each exception class provides specific error handling for different components.
"""

from typing import Any, Dict, Optional


class BaseAppException(Exception):
    """
    Base exception class for all application exceptions.
    
    Provides common functionality for error handling and logging.
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize base exception.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
            "type": self.__class__.__name__
        }


class ValidationException(BaseAppException):
    """
    Exception raised for data validation errors.
    
    Used when input data fails validation checks.
    """
    pass


class AuthenticationException(BaseAppException):
    """
    Exception raised for authentication failures.
    
    Used when user authentication fails or is missing.
    """
    pass


class AuthorizationException(BaseAppException):
    """
    Exception raised for authorization failures.
    
    Used when user lacks permission for requested operation.
    """
    pass


class DatabaseException(BaseAppException):
    """
    Exception raised for database operation errors.
    
    Used when database operations fail or encounter errors.
    """
    pass


class CacheException(BaseAppException):
    """
    Exception raised for cache operation errors.
    
    Used when Redis or other cache operations fail.
    """
    pass


class LLMException(BaseAppException):
    """
    Exception raised for LLM service errors.
    
    Used when LLM API calls fail or return errors.
    """
    pass


class AgentException(BaseAppException):
    """
    Exception raised for agent execution errors.
    
    Used when agent workflows fail or encounter errors.
    """
    pass


class ToolException(BaseAppException):
    """
    Exception raised for tool execution errors.
    
    Used when external or internal tools fail.
    """
    pass


class ConfigurationException(BaseAppException):
    """
    Exception raised for configuration errors.
    
    Used when application configuration is invalid or missing.
    """
    pass


class RateLimitException(BaseAppException):
    """
    Exception raised when rate limits are exceeded.
    
    Used by rate limiting middleware.
    """
    pass


class FileUploadException(BaseAppException):
    """
    Exception raised for file upload errors.
    
    Used when file uploads fail validation or processing.
    """
    pass


class ExternalServiceException(BaseAppException):
    """
    Exception raised for external service errors.
    
    Used when external API calls fail.
    """
    pass


class WorkflowException(BaseAppException):
    """
    Exception raised for workflow execution errors.
    
    Used when LangGraph workflows fail.
    """
    pass


class CheckpointException(BaseAppException):
    """
    Exception raised for checkpoint operation errors.
    
    Used when checkpoint save/restore operations fail.
    """
    pass


class MessageQueueException(BaseAppException):
    """
    Exception raised for message queue errors.
    
    Used when Kafka operations fail.
    """
    pass


# Exception mapping for HTTP status codes
EXCEPTION_STATUS_MAP = {
    ValidationException: 400,
    AuthenticationException: 401,
    AuthorizationException: 403,
    RateLimitException: 429,
    DatabaseException: 500,
    CacheException: 500,
    LLMException: 502,
    AgentException: 500,
    ToolException: 500,
    ConfigurationException: 500,
    FileUploadException: 400,
    ExternalServiceException: 502,
    WorkflowException: 500,
    CheckpointException: 500,
    MessageQueueException: 500,
    BaseAppException: 500,
}


def get_exception_status_code(exception: BaseAppException) -> int:
    """
    Get HTTP status code for exception.
    
    Args:
        exception: Exception instance
        
    Returns:
        int: HTTP status code
    """
    return EXCEPTION_STATUS_MAP.get(type(exception), 500)
