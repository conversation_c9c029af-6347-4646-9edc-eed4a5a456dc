"""
Security utilities for authentication and authorization.

This module provides JWT token handling, password hashing,
and other security-related functionality.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

from jose import JWTError, jwt
from passlib.context import CryptContext

from app.core.config import get_settings
from app.core.exceptions import AuthenticationException, AuthorizationException

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class SecurityManager:
    """
    Security manager for handling authentication and authorization.
    
    Provides methods for JWT token creation/validation and password hashing.
    """
    
    def __init__(self):
        """Initialize security manager."""
        self.settings = get_settings()
    
    def create_access_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create JWT access token.
        
        Args:
            data: Token payload data
            expires_delta: Token expiration time delta
            
        Returns:
            str: Encoded JWT token
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=self.settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        to_encode.update({"exp": expire, "type": "access"})
        
        encoded_jwt = jwt.encode(
            to_encode,
            self.settings.SECRET_KEY,
            algorithm=self.settings.ALGORITHM
        )
        
        return encoded_jwt
    
    def create_refresh_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create JWT refresh token.
        
        Args:
            data: Token payload data
            expires_delta: Token expiration time delta
            
        Returns:
            str: Encoded JWT token
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                days=self.settings.REFRESH_TOKEN_EXPIRE_DAYS
            )
        
        to_encode.update({"exp": expire, "type": "refresh"})
        
        encoded_jwt = jwt.encode(
            to_encode,
            self.settings.SECRET_KEY,
            algorithm=self.settings.ALGORITHM
        )
        
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """
        Verify and decode JWT token.
        
        Args:
            token: JWT token to verify
            token_type: Expected token type (access/refresh)
            
        Returns:
            Dict[str, Any]: Decoded token payload
            
        Raises:
            AuthenticationException: If token is invalid
        """
        try:
            payload = jwt.decode(
                token,
                self.settings.SECRET_KEY,
                algorithms=[self.settings.ALGORITHM]
            )
            
            # Check token type
            if payload.get("type") != token_type:
                raise AuthenticationException(
                    f"Invalid token type. Expected {token_type}"
                )
            
            # Check expiration
            exp = payload.get("exp")
            if exp is None:
                raise AuthenticationException("Token missing expiration")
            
            if datetime.utcnow() > datetime.fromtimestamp(exp):
                raise AuthenticationException("Token has expired")
            
            return payload
            
        except JWTError as e:
            raise AuthenticationException(f"Invalid token: {str(e)}")
    
    def hash_password(self, password: str) -> str:
        """
        Hash password using bcrypt.
        
        Args:
            password: Plain text password
            
        Returns:
            str: Hashed password
        """
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify password against hash.
        
        Args:
            plain_password: Plain text password
            hashed_password: Hashed password
            
        Returns:
            bool: True if password matches
        """
        return pwd_context.verify(plain_password, hashed_password)
    
    def generate_api_key(self, user_id: str, scopes: list[str]) -> str:
        """
        Generate API key for user.
        
        Args:
            user_id: User identifier
            scopes: List of permission scopes
            
        Returns:
            str: API key token
        """
        data = {
            "user_id": user_id,
            "scopes": scopes,
            "type": "api_key"
        }
        
        # API keys don't expire by default
        return jwt.encode(
            data,
            self.settings.SECRET_KEY,
            algorithm=self.settings.ALGORITHM
        )
    
    def verify_api_key(self, api_key: str) -> Dict[str, Any]:
        """
        Verify API key.
        
        Args:
            api_key: API key to verify
            
        Returns:
            Dict[str, Any]: API key payload
            
        Raises:
            AuthenticationException: If API key is invalid
        """
        try:
            payload = jwt.decode(
                api_key,
                self.settings.SECRET_KEY,
                algorithms=[self.settings.ALGORITHM]
            )
            
            if payload.get("type") != "api_key":
                raise AuthenticationException("Invalid API key format")
            
            return payload
            
        except JWTError as e:
            raise AuthenticationException(f"Invalid API key: {str(e)}")
    
    def check_permissions(
        self,
        user_scopes: list[str],
        required_scopes: list[str]
    ) -> bool:
        """
        Check if user has required permissions.
        
        Args:
            user_scopes: User's permission scopes
            required_scopes: Required permission scopes
            
        Returns:
            bool: True if user has all required permissions
        """
        return all(scope in user_scopes for scope in required_scopes)
    
    def require_permissions(
        self,
        user_scopes: list[str],
        required_scopes: list[str]
    ) -> None:
        """
        Require user to have specific permissions.
        
        Args:
            user_scopes: User's permission scopes
            required_scopes: Required permission scopes
            
        Raises:
            AuthorizationException: If user lacks required permissions
        """
        if not self.check_permissions(user_scopes, required_scopes):
            missing_scopes = set(required_scopes) - set(user_scopes)
            raise AuthorizationException(
                f"Missing required permissions: {', '.join(missing_scopes)}"
            )


# Global security manager instance
security_manager = SecurityManager()


# Convenience functions
def create_access_token(
    data: Dict[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create access token."""
    return security_manager.create_access_token(data, expires_delta)


def create_refresh_token(
    data: Dict[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create refresh token."""
    return security_manager.create_refresh_token(data, expires_delta)


def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
    """Verify token."""
    return security_manager.verify_token(token, token_type)


def hash_password(password: str) -> str:
    """Hash password."""
    return security_manager.hash_password(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password."""
    return security_manager.verify_password(plain_password, hashed_password)


def generate_api_key(user_id: str, scopes: list[str]) -> str:
    """Generate API key."""
    return security_manager.generate_api_key(user_id, scopes)


def verify_api_key(api_key: str) -> Dict[str, Any]:
    """Verify API key."""
    return security_manager.verify_api_key(api_key)


def check_permissions(user_scopes: list[str], required_scopes: list[str]) -> bool:
    """Check permissions."""
    return security_manager.check_permissions(user_scopes, required_scopes)


def require_permissions(user_scopes: list[str], required_scopes: list[str]) -> None:
    """Require permissions."""
    return security_manager.require_permissions(user_scopes, required_scopes)
