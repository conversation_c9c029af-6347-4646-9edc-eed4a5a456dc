"""
Application Constants

This module defines all constants used throughout the application.
"""

from enum import Enum
from typing import Dict, List


# API Constants
API_V1_PREFIX = "/api/v1"
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100

# Agent Types
class AgentType(str, Enum):
    """Available agent types."""
    V5_AGENT = "v5_agent"
    DEEP_RESEARCH = "deep_research"
    TASK_PLANNER = "task_planner"


# Agent Status
class AgentStatus(str, Enum):
    """Agent execution status."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


# Conversation Status
class ConversationStatus(str, Enum):
    """Conversation status."""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"


# Message Types
class MessageType(str, Enum):
    """Message types in conversations."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


# LLM Providers
class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    AZURE_OPENAI = "azure_openai"
    LOCAL = "local"


# Tool Types
class ToolType(str, Enum):
    """Tool types."""
    INTERNAL = "internal"
    EXTERNAL = "external"
    MCP = "mcp"


# File Types
ALLOWED_FILE_EXTENSIONS = {
    "txt", "md", "pdf", "docx", "doc", "rtf",
    "csv", "xlsx", "xls", "json", "xml", "yaml", "yml"
}

# MIME Types
ALLOWED_MIME_TYPES = {
    "text/plain",
    "text/markdown",
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/msword",
    "application/rtf",
    "text/csv",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-excel",
    "application/json",
    "application/xml",
    "text/xml",
    "application/x-yaml",
    "text/yaml"
}

# Rate Limiting
RATE_LIMIT_HEADERS = {
    "X-RateLimit-Limit": "X-RateLimit-Limit",
    "X-RateLimit-Remaining": "X-RateLimit-Remaining",
    "X-RateLimit-Reset": "X-RateLimit-Reset"
}

# Cache Keys
CACHE_KEYS = {
    "user_session": "user:session:{user_id}",
    "agent_state": "agent:state:{agent_id}",
    "conversation": "conversation:{conversation_id}",
    "rate_limit": "rate_limit:{user_id}:{endpoint}",
    "llm_response": "llm:response:{hash}",
    "tool_result": "tool:result:{tool_name}:{hash}"
}

# Cache TTL (in seconds)
CACHE_TTL = {
    "user_session": 3600,  # 1 hour
    "agent_state": 1800,   # 30 minutes
    "conversation": 7200,  # 2 hours
    "rate_limit": 60,      # 1 minute
    "llm_response": 3600,  # 1 hour
    "tool_result": 1800    # 30 minutes
}

# Database Constants
MAX_STRING_LENGTH = 255
MAX_TEXT_LENGTH = 65535
MAX_LONGTEXT_LENGTH = 16777215

# Agent Configuration
DEFAULT_AGENT_CONFIG = {
    "max_iterations": 50,
    "timeout_seconds": 300,
    "checkpoint_interval": 10,
    "retry_attempts": 3,
    "retry_delay": 1.0
}

# LangGraph Node Types
class NodeType(str, Enum):
    """LangGraph node types."""
    START = "start"
    END = "end"
    CONDITION = "condition"
    ACTION = "action"
    TOOL = "tool"
    LLM = "llm"


# LangGraph Edge Types
class EdgeType(str, Enum):
    """LangGraph edge types."""
    NORMAL = "normal"
    CONDITIONAL = "conditional"
    START = "start"
    END = "end"


# HTTP Status Codes
HTTP_STATUS_CODES = {
    "OK": 200,
    "CREATED": 201,
    "ACCEPTED": 202,
    "NO_CONTENT": 204,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "METHOD_NOT_ALLOWED": 405,
    "CONFLICT": 409,
    "UNPROCESSABLE_ENTITY": 422,
    "TOO_MANY_REQUESTS": 429,
    "INTERNAL_SERVER_ERROR": 500,
    "BAD_GATEWAY": 502,
    "SERVICE_UNAVAILABLE": 503,
    "GATEWAY_TIMEOUT": 504
}

# Error Codes
ERROR_CODES = {
    "VALIDATION_ERROR": "VALIDATION_ERROR",
    "AUTHENTICATION_ERROR": "AUTHENTICATION_ERROR",
    "AUTHORIZATION_ERROR": "AUTHORIZATION_ERROR",
    "DATABASE_ERROR": "DATABASE_ERROR",
    "CACHE_ERROR": "CACHE_ERROR",
    "LLM_ERROR": "LLM_ERROR",
    "AGENT_ERROR": "AGENT_ERROR",
    "TOOL_ERROR": "TOOL_ERROR",
    "RATE_LIMIT_ERROR": "RATE_LIMIT_ERROR",
    "FILE_UPLOAD_ERROR": "FILE_UPLOAD_ERROR",
    "EXTERNAL_SERVICE_ERROR": "EXTERNAL_SERVICE_ERROR",
    "WORKFLOW_ERROR": "WORKFLOW_ERROR",
    "CHECKPOINT_ERROR": "CHECKPOINT_ERROR",
    "MESSAGE_QUEUE_ERROR": "MESSAGE_QUEUE_ERROR",
    "CONFIGURATION_ERROR": "CONFIGURATION_ERROR",
    "INTERNAL_ERROR": "INTERNAL_ERROR"
}

# Logging Constants
LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
LOG_FORMATS = ["json", "text"]

# Monitoring Constants
METRICS_NAMESPACE = "super_v5"
HEALTH_CHECK_TIMEOUT = 5.0

# Security Constants
MIN_PASSWORD_LENGTH = 8
MAX_PASSWORD_LENGTH = 128
PASSWORD_REQUIREMENTS = {
    "min_length": 8,
    "require_uppercase": True,
    "require_lowercase": True,
    "require_digits": True,
    "require_special_chars": True
}

# JWT Constants
JWT_ALGORITHM = "HS256"
JWT_TOKEN_TYPES = ["access", "refresh", "api_key"]

# Pagination Constants
DEFAULT_PAGE = 1
MIN_PAGE_SIZE = 1
MAX_PAGE_SIZE = 100

# Agent Prompt Templates
AGENT_PROMPT_TEMPLATES = {
    "system": "You are a helpful AI assistant. Follow the instructions carefully.",
    "user_greeting": "Hello! How can I help you today?",
    "error_message": "I apologize, but I encountered an error. Please try again.",
    "completion_message": "Task completed successfully."
}

# Tool Configuration
TOOL_TIMEOUT_SECONDS = 30
MAX_TOOL_RETRIES = 3
TOOL_RETRY_DELAY = 1.0

# File Upload Constants
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
UPLOAD_CHUNK_SIZE = 8192  # 8KB

# Environment Constants
ENVIRONMENTS = ["development", "staging", "production", "testing"]

# Feature Flags
FEATURE_FLAGS = {
    "enable_external_tools": True,
    "enable_file_upload": True,
    "enable_rate_limiting": True,
    "enable_metrics": True,
    "enable_caching": True,
    "enable_checkpoints": True
}
