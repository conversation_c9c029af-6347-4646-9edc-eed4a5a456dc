"""
Application Configuration Management

This module handles all application configuration including environment variables,
Apollo configuration center integration, and settings validation.
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    This class defines all configuration parameters for the application,
    with automatic loading from environment variables and validation.
    """
    
    # Application Configuration
    APP_NAME: str = Field(default="super-v5", description="Application name")
    APP_VERSION: str = Field(default="0.1.0", description="Application version")
    APP_DESCRIPTION: str = Field(
        default="Super-V5 Enterprise LLM Application Backend Service Platform",
        description="Application description"
    )
    DEBUG: bool = Field(default=False, description="Debug mode")
    ENVIRONMENT: str = Field(default="development", description="Environment")
    
    # Server Configuration
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port")
    WORKERS: int = Field(default=1, description="Number of worker processes")
    RELOAD: bool = Field(default=True, description="Auto-reload on code changes")
    ALLOWED_HOSTS: List[str] = Field(default=["*"], description="Allowed hosts")
    
    # Database Configuration
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://username:password@localhost:5432/super_v5",
        description="Database connection URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, description="Database pool size")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, description="Database max overflow")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, description="Database pool timeout")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, description="Database pool recycle")
    
    # Redis Configuration
    REDIS_URL: str = Field(
        default="redis://localhost:6379/0",
        description="Redis connection URL"
    )
    REDIS_PASSWORD: Optional[str] = Field(default=None, description="Redis password")
    REDIS_DB: int = Field(default=0, description="Redis database number")
    REDIS_MAX_CONNECTIONS: int = Field(default=20, description="Redis max connections")
    REDIS_SOCKET_TIMEOUT: int = Field(default=5, description="Redis socket timeout")
    REDIS_SOCKET_CONNECT_TIMEOUT: int = Field(
        default=5, description="Redis socket connect timeout"
    )
    
    # Security Configuration
    SECRET_KEY: str = Field(
        default="your-super-secret-key-change-this-in-production",
        description="Secret key for JWT tokens"
    )
    ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30, description="Access token expiration in minutes"
    )
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(
        default=7, description="Refresh token expiration in days"
    )
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="CORS allowed origins"
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(
        default=True, description="CORS allow credentials"
    )
    CORS_ALLOW_METHODS: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="CORS allowed methods"
    )
    CORS_ALLOW_HEADERS: List[str] = Field(
        default=["*"], description="CORS allowed headers"
    )
    
    # LLM Configuration
    DEFAULT_LLM_PROVIDER: str = Field(default="openai", description="Default LLM provider")
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API key")
    OPENAI_BASE_URL: str = Field(
        default="https://api.openai.com/v1", description="OpenAI base URL"
    )
    OPENAI_MODEL: str = Field(
        default="gpt-4-turbo-preview", description="Default OpenAI model"
    )
    OPENAI_MAX_TOKENS: int = Field(default=4096, description="OpenAI max tokens")
    OPENAI_TEMPERATURE: float = Field(default=0.7, description="OpenAI temperature")
    
    # Alternative LLM Providers
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, description="Anthropic API key")
    AZURE_OPENAI_API_KEY: Optional[str] = Field(
        default=None, description="Azure OpenAI API key"
    )
    AZURE_OPENAI_ENDPOINT: Optional[str] = Field(
        default=None, description="Azure OpenAI endpoint"
    )
    
    # LangGraph Configuration
    LANGGRAPH_CHECKPOINT_BACKEND: str = Field(
        default="redis", description="LangGraph checkpoint backend"
    )
    LANGGRAPH_MAX_ITERATIONS: int = Field(
        default=50, description="LangGraph max iterations"
    )
    LANGGRAPH_TIMEOUT_SECONDS: int = Field(
        default=300, description="LangGraph timeout in seconds"
    )
    
    # Kafka Configuration
    KAFKA_BOOTSTRAP_SERVERS: str = Field(
        default="localhost:9092", description="Kafka bootstrap servers"
    )
    KAFKA_SECURITY_PROTOCOL: str = Field(
        default="PLAINTEXT", description="Kafka security protocol"
    )
    KAFKA_SASL_MECHANISM: str = Field(
        default="PLAIN", description="Kafka SASL mechanism"
    )
    KAFKA_SASL_USERNAME: Optional[str] = Field(
        default=None, description="Kafka SASL username"
    )
    KAFKA_SASL_PASSWORD: Optional[str] = Field(
        default=None, description="Kafka SASL password"
    )
    
    # Apollo Configuration Center
    APOLLO_APP_ID: str = Field(default="super-v5", description="Apollo app ID")
    APOLLO_CLUSTER: str = Field(default="default", description="Apollo cluster")
    APOLLO_NAMESPACE: str = Field(default="application", description="Apollo namespace")
    APOLLO_META_SERVER: str = Field(
        default="http://localhost:8080", description="Apollo meta server"
    )
    
    # Monitoring & Logging
    LOG_LEVEL: str = Field(default="INFO", description="Log level")
    LOG_FORMAT: str = Field(default="json", description="Log format")
    ENABLE_METRICS: bool = Field(default=True, description="Enable metrics")
    METRICS_PORT: int = Field(default=9090, description="Metrics port")
    SENTRY_DSN: Optional[str] = Field(default=None, description="Sentry DSN")
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, description="Enable rate limiting")
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = Field(
        default=60, description="Rate limit requests per minute"
    )
    RATE_LIMIT_BURST: int = Field(default=10, description="Rate limit burst")
    
    # File Upload
    MAX_UPLOAD_SIZE: int = Field(default=10485760, description="Max upload size in bytes")
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["txt", "pdf", "docx", "md"], description="Allowed file types"
    )
    
    # Agent Configuration
    DEFAULT_AGENT_TYPE: str = Field(default="v5_agent", description="Default agent type")
    AGENT_TIMEOUT_SECONDS: int = Field(
        default=300, description="Agent timeout in seconds"
    )
    MAX_CONCURRENT_AGENTS: int = Field(
        default=10, description="Max concurrent agents"
    )
    
    # Tool Configuration
    ENABLE_EXTERNAL_TOOLS: bool = Field(
        default=True, description="Enable external tools"
    )
    TOOL_TIMEOUT_SECONDS: int = Field(
        default=30, description="Tool timeout in seconds"
    )
    MAX_TOOL_RETRIES: int = Field(default=3, description="Max tool retries")
    
    # Development & Testing
    TESTING: bool = Field(default=False, description="Testing mode")
    TEST_DATABASE_URL: str = Field(
        default="postgresql+asyncpg://test:test@localhost:5432/super_v5_test",
        description="Test database URL"
    )
    PYTEST_TIMEOUT: int = Field(default=30, description="Pytest timeout")
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v: str) -> str:
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v: str) -> str:
        """Validate environment."""
        valid_envs = ["development", "staging", "production", "testing"]
        if v.lower() not in valid_envs:
            raise ValueError(f"ENVIRONMENT must be one of {valid_envs}")
        return v.lower()
    
    @validator("SECRET_KEY")
    def validate_secret_key(cls, v: str) -> str:
        """Validate secret key."""
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.
    
    Returns:
        Settings: Application settings instance
    """
    return Settings()


# TODO: Implement Apollo configuration center integration
class ApolloConfig:
    """
    Apollo Configuration Center integration.
    
    This class will handle dynamic configuration updates from Apollo.
    """
    
    def __init__(self, settings: Settings):
        """Initialize Apollo configuration."""
        self.settings = settings
        # TODO: Initialize Apollo client
    
    async def start(self) -> None:
        """Start Apollo configuration monitoring."""
        # TODO: Implement Apollo configuration monitoring
        pass
    
    async def stop(self) -> None:
        """Stop Apollo configuration monitoring."""
        # TODO: Implement Apollo configuration cleanup
        pass
    
    def get_config(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get configuration value from Apollo."""
        # TODO: Implement Apollo configuration retrieval
        return default
    
    def update_config(self, key: str, value: str) -> None:
        """Update configuration value in Apollo."""
        # TODO: Implement Apollo configuration update
        pass
