"""
FastAPI Application Entry Point

This module contains the main FastAPI application instance and configuration.
It sets up middleware, routes, exception handlers, and application lifecycle events.
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from app.api.middleware.auth import AuthMiddleware
from app.api.middleware.logging import LoggingMiddleware
from app.api.middleware.rate_limit import RateLimitMiddleware
from app.api.v1 import api_router
from app.core.config import get_settings
from app.core.exceptions import (
    AgentException,
    DatabaseException,
    LLMException,
    ValidationException,
)
from app.infrastructure.database.session import init_db
from app.infrastructure.cache.redis_client import init_redis
from app.models.schemas.response import APIResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.
    
    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    logger.info("Starting Super-V5 LLM Backend Service...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Initialize Redis
    await init_redis()
    logger.info("Redis initialized")
    
    # TODO: Initialize agent registry
    # TODO: Initialize tool registry
    # TODO: Initialize monitoring
    
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Super-V5 LLM Backend Service...")
    
    # TODO: Cleanup resources
    # TODO: Close database connections
    # TODO: Close Redis connections
    
    logger.info("Application shutdown complete")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    settings = get_settings()
    
    app = FastAPI(
        title=settings.APP_NAME,
        description=settings.APP_DESCRIPTION,
        version=settings.APP_VERSION,
        debug=settings.DEBUG,
        lifespan=lifespan,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
    )
    
    # Add middleware
    setup_middleware(app)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    # Include routers
    app.include_router(api_router, prefix="/api")
    
    return app


def setup_middleware(app: FastAPI) -> None:
    """
    Configure application middleware.
    
    Args:
        app: FastAPI application instance
    """
    settings = get_settings()
    
    # Trusted Host Middleware
    if not settings.DEBUG:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.ALLOWED_HOSTS
        )
    
    # CORS Middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )
    
    # Custom Middleware
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(AuthMiddleware)
    
    if settings.RATE_LIMIT_ENABLED:
        app.add_middleware(RateLimitMiddleware)


def setup_exception_handlers(app: FastAPI) -> None:
    """
    Configure application exception handlers.
    
    Args:
        app: FastAPI application instance
    """
    
    @app.exception_handler(ValidationException)
    async def validation_exception_handler(
        request: Request, exc: ValidationException
    ) -> JSONResponse:
        """Handle validation exceptions."""
        return JSONResponse(
            status_code=400,
            content=APIResponse(
                success=False,
                message=str(exc),
                error_code="VALIDATION_ERROR"
            ).model_dump()
        )
    
    @app.exception_handler(DatabaseException)
    async def database_exception_handler(
        request: Request, exc: DatabaseException
    ) -> JSONResponse:
        """Handle database exceptions."""
        logger.error(f"Database error: {exc}")
        return JSONResponse(
            status_code=500,
            content=APIResponse(
                success=False,
                message="Database operation failed",
                error_code="DATABASE_ERROR"
            ).model_dump()
        )
    
    @app.exception_handler(LLMException)
    async def llm_exception_handler(
        request: Request, exc: LLMException
    ) -> JSONResponse:
        """Handle LLM exceptions."""
        logger.error(f"LLM error: {exc}")
        return JSONResponse(
            status_code=502,
            content=APIResponse(
                success=False,
                message="LLM service unavailable",
                error_code="LLM_ERROR"
            ).model_dump()
        )
    
    @app.exception_handler(AgentException)
    async def agent_exception_handler(
        request: Request, exc: AgentException
    ) -> JSONResponse:
        """Handle agent exceptions."""
        logger.error(f"Agent error: {exc}")
        return JSONResponse(
            status_code=500,
            content=APIResponse(
                success=False,
                message="Agent execution failed",
                error_code="AGENT_ERROR"
            ).model_dump()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(
        request: Request, exc: Exception
    ) -> JSONResponse:
        """Handle general exceptions."""
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content=APIResponse(
                success=False,
                message="Internal server error",
                error_code="INTERNAL_ERROR"
            ).model_dump()
        )


# Create the application instance
app = create_application()


def main() -> None:
    """
    Main entry point for running the application.
    
    This function is used when running the application directly
    or through the CLI command defined in pyproject.toml.
    """
    import uvicorn
    
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        workers=settings.WORKERS if not settings.RELOAD else 1,
        log_level=settings.LOG_LEVEL.lower(),
    )


if __name__ == "__main__":
    main()
