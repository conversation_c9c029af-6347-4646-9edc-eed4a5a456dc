"""
V5 Agent Implementation

This module contains the default V5 system agent implementation
using LangGraph for workflow orchestration.
"""

import logging
from typing import Any, Dict, List, Optional

from app.core.exceptions import AgentException
from app.agents.business.v5_agent.graph import V5AgentGraph
from app.agents.business.v5_agent.prompts import V5AgentPrompts

logger = logging.getLogger(__name__)


class V5Agent:
    """
    Default V5 system agent.
    
    This agent provides general-purpose conversational AI capabilities
    with tool integration and workflow management.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize V5 agent.
        
        Args:
            config: Agent configuration
        """
        self.config = config
        self.name = config.get("name", "V5 Agent")
        self.version = config.get("version", "1.0.0")
        self.description = config.get("description", "Default V5 system agent")
        
        # Agent parameters
        self.max_iterations = config.get("max_iterations", 50)
        self.timeout = config.get("timeout", 300)
        self.temperature = config.get("temperature", 0.7)
        
        # Initialize components
        self.prompts = V5AgentPrompts()
        self.graph = None
        
        # Agent state
        self.is_initialized = False
        
        logger.info(f"Initialized V5 agent: {self.name}")
    
    async def initialize(self) -> None:
        """
        Initialize the agent and its components.
        
        This method should be called before using the agent.
        """
        try:
            # Initialize the LangGraph workflow
            self.graph = V5AgentGraph(self.config)
            await self.graph.initialize()
            
            self.is_initialized = True
            logger.info(f"V5 agent {self.name} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize V5 agent: {e}")
            raise AgentException(f"Agent initialization failed: {str(e)}")
    
    async def execute(
        self,
        input_data: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute the agent with given input.
        
        Args:
            input_data: Input data for the agent
            context: Optional execution context
            
        Returns:
            Dict[str, Any]: Agent execution result
            
        Raises:
            AgentException: If execution fails
        """
        if not self.is_initialized:
            await self.initialize()
        
        try:
            # Prepare execution context
            execution_context = {
                "agent_name": self.name,
                "agent_version": self.version,
                "max_iterations": self.max_iterations,
                "timeout": self.timeout,
                **(context or {})
            }
            
            # Execute the graph workflow
            result = await self.graph.execute(input_data, execution_context)
            
            logger.info(f"V5 agent execution completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"V5 agent execution failed: {e}")
            raise AgentException(f"Agent execution failed: {str(e)}")
    
    async def stream_execute(
        self,
        input_data: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Any:  # AsyncGenerator
        """
        Execute the agent with streaming output.
        
        Args:
            input_data: Input data for the agent
            context: Optional execution context
            
        Yields:
            Dict[str, Any]: Streaming execution updates
            
        Raises:
            AgentException: If execution fails
        """
        if not self.is_initialized:
            await self.initialize()
        
        try:
            # Prepare execution context
            execution_context = {
                "agent_name": self.name,
                "agent_version": self.version,
                "max_iterations": self.max_iterations,
                "timeout": self.timeout,
                "streaming": True,
                **(context or {})
            }
            
            # Execute the graph workflow with streaming
            async for update in self.graph.stream_execute(input_data, execution_context):
                yield update
            
            logger.info(f"V5 agent streaming execution completed")
            
        except Exception as e:
            logger.error(f"V5 agent streaming execution failed: {e}")
            raise AgentException(f"Agent streaming execution failed: {str(e)}")
    
    def get_capabilities(self) -> List[str]:
        """
        Get list of agent capabilities.
        
        Returns:
            List[str]: Agent capabilities
        """
        return [
            "conversation",
            "question_answering",
            "task_execution",
            "tool_usage",
            "context_awareness",
            "multi_turn_dialogue"
        ]
    
    def get_tools(self) -> List[str]:
        """
        Get list of available tools.
        
        Returns:
            List[str]: Available tools
        """
        # TODO: Get tools from tool registry
        return [
            "search",
            "calculator",
            "database_query",
            "file_operations"
        ]
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get agent status information.
        
        Returns:
            Dict[str, Any]: Agent status
        """
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "is_initialized": self.is_initialized,
            "capabilities": self.get_capabilities(),
            "tools": self.get_tools(),
            "config": {
                "max_iterations": self.max_iterations,
                "timeout": self.timeout,
                "temperature": self.temperature
            }
        }
    
    async def cleanup(self) -> None:
        """
        Clean up agent resources.
        
        This method should be called when the agent is no longer needed.
        """
        try:
            if self.graph:
                await self.graph.cleanup()
            
            self.is_initialized = False
            logger.info(f"V5 agent {self.name} cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error cleaning up V5 agent: {e}")
    
    def __repr__(self) -> str:
        """String representation of the agent."""
        return f"V5Agent(name={self.name}, version={self.version})"
