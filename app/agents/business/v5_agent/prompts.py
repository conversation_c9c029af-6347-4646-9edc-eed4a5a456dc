"""
V5 Agent Prompts

This module contains prompt templates and prompt management for the V5 agent.
"""

from typing import Any, Dict, List, Optional


class V5AgentPrompts:
    """
    Prompt templates and management for V5 agent.
    
    Contains all prompt templates used by the V5 agent for different
    scenarios and conversation contexts.
    """
    
    def __init__(self):
        """Initialize V5 agent prompts."""
        self.system_prompt = self._get_system_prompt()
        self.conversation_prompt = self._get_conversation_prompt()
        self.tool_usage_prompt = self._get_tool_usage_prompt()
        self.error_handling_prompt = self._get_error_handling_prompt()
    
    def _get_system_prompt(self) -> str:
        """
        Get the main system prompt for V5 agent.
        
        Returns:
            str: System prompt template
        """
        return """You are V5 Agent, an advanced AI assistant designed to help users with a wide variety of tasks. You are part of the Super-V5 enterprise platform and have access to various tools and capabilities.

Your core characteristics:
- Helpful, harmless, and honest in all interactions
- Professional yet approachable communication style
- Ability to break down complex problems into manageable steps
- Proactive in suggesting solutions and alternatives
- Transparent about your capabilities and limitations

Your capabilities include:
- Answering questions and providing explanations
- Helping with analysis and research tasks
- Assisting with planning and organization
- Using tools when appropriate to gather information or perform actions
- Maintaining context across multi-turn conversations

Guidelines for interaction:
1. Always strive to understand the user's intent and provide relevant, accurate responses
2. When uncertain, ask clarifying questions rather than making assumptions
3. Use tools when they can provide better or more current information
4. Explain your reasoning when making recommendations
5. Be concise but thorough in your responses
6. Maintain a helpful and professional tone throughout the conversation

Remember: You are designed to be a reliable assistant that users can depend on for accurate information and thoughtful assistance."""
    
    def _get_conversation_prompt(self) -> str:
        """
        Get the conversation continuation prompt.
        
        Returns:
            str: Conversation prompt template
        """
        return """Continue the conversation naturally based on the context and user's message. 

Consider:
- The conversation history and context
- The user's current request or question
- Any relevant information from previous exchanges
- Whether tools might be helpful for this request

Respond in a way that:
- Directly addresses the user's needs
- Builds upon the existing conversation context
- Provides value and actionable information
- Maintains the established tone and relationship

If the user's request is unclear, ask for clarification. If you need additional information to provide a complete answer, use available tools or ask the user for more details."""
    
    def _get_tool_usage_prompt(self) -> str:
        """
        Get the tool usage decision prompt.
        
        Returns:
            str: Tool usage prompt template
        """
        return """Analyze whether tools should be used to better respond to the user's request.

Consider using tools when:
- The user asks for current information that might change over time
- You need to search for specific facts or data
- The request involves calculations or data processing
- You need to access external systems or databases
- The user explicitly asks for real-time or updated information

Available tools and their purposes:
- Search: For finding current information on the web
- Calculator: For mathematical calculations and computations
- Database Query: For accessing structured data
- File Operations: For reading, writing, or processing files

Decision criteria:
1. Will a tool provide more accurate or current information?
2. Is the user's request something that requires external data?
3. Can I provide a complete answer without tools?
4. Would tool usage significantly improve the response quality?

If you decide to use a tool, clearly explain why it's necessary and what information you're seeking."""
    
    def _get_error_handling_prompt(self) -> str:
        """
        Get the error handling prompt.
        
        Returns:
            str: Error handling prompt template
        """
        return """When an error occurs during task execution:

1. Acknowledge the issue clearly and honestly
2. Explain what went wrong in simple terms
3. Suggest alternative approaches if possible
4. Offer to try again or take a different approach
5. Ask for clarification if the error might be due to misunderstanding

Error response guidelines:
- Be transparent about limitations
- Don't make excuses or blame external factors
- Focus on solutions and next steps
- Maintain a helpful and professional tone
- Offer to escalate to human support if needed

Example responses:
- "I encountered an issue while trying to [action]. Let me try a different approach..."
- "I'm unable to [action] due to [reason]. Would you like me to try [alternative]?"
- "There seems to be a problem with [system/tool]. I can still help you with [alternative approach]."

Always prioritize the user's needs and work toward finding a solution, even if the original approach doesn't work."""
    
    def format_system_message(
        self,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Format the system message with context.
        
        Args:
            context: Optional context information
            
        Returns:
            str: Formatted system message
        """
        base_prompt = self.system_prompt
        
        if context:
            # Add context-specific information
            if context.get("user_name"):
                base_prompt += f"\n\nUser: {context['user_name']}"
            
            if context.get("conversation_context"):
                base_prompt += f"\n\nConversation Context: {context['conversation_context']}"
            
            if context.get("available_tools"):
                tools_list = ", ".join(context["available_tools"])
                base_prompt += f"\n\nAvailable Tools: {tools_list}"
        
        return base_prompt
    
    def format_conversation_message(
        self,
        user_message: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Format a conversation message with history and context.
        
        Args:
            user_message: Current user message
            conversation_history: Previous conversation messages
            context: Additional context information
            
        Returns:
            str: Formatted conversation prompt
        """
        prompt = self.conversation_prompt
        
        if conversation_history:
            prompt += "\n\nConversation History:\n"
            for msg in conversation_history[-5:]:  # Last 5 messages
                role = msg.get("role", "unknown")
                content = msg.get("content", "")
                prompt += f"{role.title()}: {content}\n"
        
        prompt += f"\n\nCurrent User Message: {user_message}"
        
        if context:
            if context.get("task_context"):
                prompt += f"\n\nTask Context: {context['task_context']}"
            
            if context.get("constraints"):
                prompt += f"\n\nConstraints: {context['constraints']}"
        
        return prompt
    
    def format_tool_decision_prompt(
        self,
        user_request: str,
        available_tools: List[str],
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Format prompt for tool usage decision.
        
        Args:
            user_request: User's request
            available_tools: List of available tools
            context: Additional context
            
        Returns:
            str: Formatted tool decision prompt
        """
        prompt = self.tool_usage_prompt
        prompt += f"\n\nUser Request: {user_request}"
        prompt += f"\n\nAvailable Tools: {', '.join(available_tools)}"
        
        if context and context.get("conversation_context"):
            prompt += f"\n\nConversation Context: {context['conversation_context']}"
        
        prompt += "\n\nDecision: Should tools be used? If yes, which tools and why?"
        
        return prompt
    
    def format_error_response(
        self,
        error_type: str,
        error_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Format an error response message.
        
        Args:
            error_type: Type of error that occurred
            error_message: Error message details
            context: Additional context
            
        Returns:
            str: Formatted error response
        """
        base_response = self.error_handling_prompt
        
        response = f"I encountered a {error_type} while processing your request. "
        
        if "timeout" in error_message.lower():
            response += "The operation took longer than expected. "
        elif "connection" in error_message.lower():
            response += "There was a connectivity issue. "
        elif "permission" in error_message.lower():
            response += "I don't have the necessary permissions for this action. "
        else:
            response += f"Here's what happened: {error_message}. "
        
        response += "Let me try to help you in a different way. "
        
        if context and context.get("alternative_suggestions"):
            response += f"I can try: {', '.join(context['alternative_suggestions'])}. "
        
        response += "Would you like me to proceed with an alternative approach?"
        
        return response
