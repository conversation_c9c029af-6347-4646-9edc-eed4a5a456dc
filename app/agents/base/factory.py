"""
Agent Factory

This module implements the factory pattern for creating agent instances
with proper configuration and dependency injection.
"""

import logging
from typing import Any, Dict, Optional, Type

from app.core.constants import AgentType
from app.core.exceptions import AgentException, ConfigurationException

logger = logging.getLogger(__name__)


class AgentFactory:
    """
    Factory for creating agent instances.
    
    Implements the Factory pattern to create agents with proper
    configuration and dependency injection.
    """
    
    def __init__(self):
        """Initialize agent factory."""
        self._agent_classes: Dict[str, Type] = {}
        self._default_configs: Dict[str, Dict[str, Any]] = {}
    
    def register_agent(
        self,
        agent_type: str,
        agent_class: Type,
        default_config: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Register an agent class with the factory.
        
        Args:
            agent_type: Agent type identifier
            agent_class: Agent class to register
            default_config: Default configuration for the agent
        """
        self._agent_classes[agent_type] = agent_class
        self._default_configs[agent_type] = default_config or {}
        
        logger.info(f"Registered agent type: {agent_type}")
    
    def create_agent(
        self,
        agent_type: str,
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Any:
        """
        Create an agent instance.
        
        Args:
            agent_type: Type of agent to create
            config: Agent configuration
            **kwargs: Additional parameters
            
        Returns:
            Agent instance
            
        Raises:
            AgentException: If agent creation fails
            ConfigurationException: If configuration is invalid
        """
        if agent_type not in self._agent_classes:
            raise AgentException(f"Unknown agent type: {agent_type}")
        
        try:
            # Get agent class
            agent_class = self._agent_classes[agent_type]
            
            # Merge default config with provided config
            merged_config = self._default_configs[agent_type].copy()
            if config:
                merged_config.update(config)
            
            # Validate configuration
            self._validate_config(agent_type, merged_config)
            
            # Create agent instance
            agent = agent_class(config=merged_config, **kwargs)
            
            logger.info(f"Created agent of type: {agent_type}")
            return agent
            
        except Exception as e:
            logger.error(f"Failed to create agent {agent_type}: {e}")
            raise AgentException(f"Agent creation failed: {str(e)}")
    
    def get_available_types(self) -> list[str]:
        """
        Get list of available agent types.
        
        Returns:
            list[str]: Available agent types
        """
        return list(self._agent_classes.keys())
    
    def get_agent_class(self, agent_type: str) -> Optional[Type]:
        """
        Get agent class for a type.
        
        Args:
            agent_type: Agent type
            
        Returns:
            Optional[Type]: Agent class or None if not found
        """
        return self._agent_classes.get(agent_type)
    
    def get_default_config(self, agent_type: str) -> Dict[str, Any]:
        """
        Get default configuration for an agent type.
        
        Args:
            agent_type: Agent type
            
        Returns:
            Dict[str, Any]: Default configuration
        """
        return self._default_configs.get(agent_type, {}).copy()
    
    def _validate_config(self, agent_type: str, config: Dict[str, Any]) -> None:
        """
        Validate agent configuration.
        
        Args:
            agent_type: Agent type
            config: Configuration to validate
            
        Raises:
            ConfigurationException: If configuration is invalid
        """
        # Basic validation - can be extended per agent type
        required_fields = ["name", "version"]
        
        for field in required_fields:
            if field not in config:
                raise ConfigurationException(
                    f"Missing required field '{field}' for agent type {agent_type}"
                )
        
        # Validate agent-specific requirements
        if agent_type == AgentType.V5_AGENT:
            self._validate_v5_agent_config(config)
        elif agent_type == AgentType.DEEP_RESEARCH:
            self._validate_deep_research_config(config)
        elif agent_type == AgentType.TASK_PLANNER:
            self._validate_task_planner_config(config)
    
    def _validate_v5_agent_config(self, config: Dict[str, Any]) -> None:
        """
        Validate V5 agent configuration.
        
        Args:
            config: Configuration to validate
        """
        # TODO: Implement V5 agent specific validation
        pass
    
    def _validate_deep_research_config(self, config: Dict[str, Any]) -> None:
        """
        Validate deep research agent configuration.
        
        Args:
            config: Configuration to validate
        """
        # TODO: Implement deep research agent specific validation
        pass
    
    def _validate_task_planner_config(self, config: Dict[str, Any]) -> None:
        """
        Validate task planner agent configuration.
        
        Args:
            config: Configuration to validate
        """
        # TODO: Implement task planner agent specific validation
        pass


# Global agent factory instance
agent_factory = AgentFactory()


def register_default_agents() -> None:
    """
    Register default agent types with the factory.
    
    This function should be called during application startup
    to register all available agent types.
    """
    # TODO: Import and register agent classes
    # from app.agents.business.v5_agent.agent import V5Agent
    # from app.agents.business.deep_research.agent import DeepResearchAgent
    # from app.agents.business.task_planner.agent import TaskPlannerAgent
    
    # agent_factory.register_agent(
    #     AgentType.V5_AGENT,
    #     V5Agent,
    #     {
    #         "name": "V5 Default Agent",
    #         "version": "1.0.0",
    #         "description": "Default V5 system agent",
    #         "max_iterations": 50,
    #         "timeout": 300
    #     }
    # )
    
    # agent_factory.register_agent(
    #     AgentType.DEEP_RESEARCH,
    #     DeepResearchAgent,
    #     {
    #         "name": "Deep Research Agent",
    #         "version": "1.0.0",
    #         "description": "Agent for deep research tasks",
    #         "max_iterations": 100,
    #         "timeout": 600
    #     }
    # )
    
    # agent_factory.register_agent(
    #     AgentType.TASK_PLANNER,
    #     TaskPlannerAgent,
    #     {
    #         "name": "Task Planner Agent",
    #         "version": "1.0.0",
    #         "description": "Agent for task planning and decomposition",
    #         "max_iterations": 30,
    #         "timeout": 180
    #     }
    # )
    
    logger.info("Default agents registered with factory")


def create_agent(
    agent_type: str,
    config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Any:
    """
    Convenience function to create an agent.
    
    Args:
        agent_type: Type of agent to create
        config: Agent configuration
        **kwargs: Additional parameters
        
    Returns:
        Agent instance
    """
    return agent_factory.create_agent(agent_type, config, **kwargs)


def get_available_agent_types() -> list[str]:
    """
    Get list of available agent types.
    
    Returns:
        list[str]: Available agent types
    """
    return agent_factory.get_available_types()
