"""
Agent Registry

This module implements a registry for managing active agent instances
and their lifecycle.
"""

import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from app.core.constants import AgentStatus
from app.core.exceptions import AgentException

logger = logging.getLogger(__name__)


class AgentInstance:
    """
    Represents an active agent instance in the registry.
    """
    
    def __init__(
        self,
        agent_id: str,
        agent_type: str,
        agent: Any,
        config: Dict[str, Any],
        created_by: Optional[str] = None
    ):
        """
        Initialize agent instance.
        
        Args:
            agent_id: Unique agent instance ID
            agent_type: Type of agent
            agent: Agent instance
            config: Agent configuration
            created_by: User who created the agent
        """
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.agent = agent
        self.config = config
        self.created_by = created_by
        self.created_at = datetime.utcnow()
        self.last_used_at = datetime.utcnow()
        self.status = AgentStatus.IDLE
        self.execution_count = 0
        self.metadata: Dict[str, Any] = {}
    
    def update_last_used(self) -> None:
        """Update last used timestamp."""
        self.last_used_at = datetime.utcnow()
    
    def increment_execution_count(self) -> None:
        """Increment execution count."""
        self.execution_count += 1
        self.update_last_used()
    
    def set_status(self, status: AgentStatus) -> None:
        """
        Set agent status.
        
        Args:
            status: New agent status
        """
        self.status = status
        self.update_last_used()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert agent instance to dictionary.
        
        Returns:
            Dict[str, Any]: Agent instance data
        """
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "config": self.config,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat(),
            "last_used_at": self.last_used_at.isoformat(),
            "status": self.status,
            "execution_count": self.execution_count,
            "metadata": self.metadata
        }


class AgentRegistry:
    """
    Registry for managing active agent instances.
    
    Provides centralized management of agent lifecycle including
    creation, tracking, and cleanup of agent instances.
    """
    
    def __init__(self, max_instances: int = 100):
        """
        Initialize agent registry.
        
        Args:
            max_instances: Maximum number of concurrent agent instances
        """
        self.max_instances = max_instances
        self._instances: Dict[str, AgentInstance] = {}
        self._user_instances: Dict[str, List[str]] = {}
    
    def register_agent(
        self,
        agent: Any,
        agent_type: str,
        config: Dict[str, Any],
        created_by: Optional[str] = None,
        agent_id: Optional[str] = None
    ) -> str:
        """
        Register a new agent instance.
        
        Args:
            agent: Agent instance
            agent_type: Type of agent
            config: Agent configuration
            created_by: User who created the agent
            agent_id: Optional custom agent ID
            
        Returns:
            str: Agent instance ID
            
        Raises:
            AgentException: If registration fails
        """
        # Check instance limit
        if len(self._instances) >= self.max_instances:
            self._cleanup_old_instances()
            
            if len(self._instances) >= self.max_instances:
                raise AgentException("Maximum number of agent instances reached")
        
        # Generate agent ID if not provided
        if agent_id is None:
            agent_id = str(uuid.uuid4())
        
        # Check for duplicate ID
        if agent_id in self._instances:
            raise AgentException(f"Agent instance {agent_id} already exists")
        
        # Create agent instance
        instance = AgentInstance(
            agent_id=agent_id,
            agent_type=agent_type,
            agent=agent,
            config=config,
            created_by=created_by
        )
        
        # Register instance
        self._instances[agent_id] = instance
        
        # Track user instances
        if created_by:
            if created_by not in self._user_instances:
                self._user_instances[created_by] = []
            self._user_instances[created_by].append(agent_id)
        
        logger.info(f"Registered agent instance: {agent_id} (type: {agent_type})")
        return agent_id
    
    def get_agent(self, agent_id: str) -> Optional[Any]:
        """
        Get agent instance by ID.
        
        Args:
            agent_id: Agent instance ID
            
        Returns:
            Optional[Any]: Agent instance or None if not found
        """
        instance = self._instances.get(agent_id)
        if instance:
            instance.update_last_used()
            return instance.agent
        return None
    
    def get_agent_instance(self, agent_id: str) -> Optional[AgentInstance]:
        """
        Get agent instance metadata by ID.
        
        Args:
            agent_id: Agent instance ID
            
        Returns:
            Optional[AgentInstance]: Agent instance or None if not found
        """
        instance = self._instances.get(agent_id)
        if instance:
            instance.update_last_used()
        return instance
    
    def unregister_agent(self, agent_id: str) -> bool:
        """
        Unregister an agent instance.
        
        Args:
            agent_id: Agent instance ID
            
        Returns:
            bool: True if unregistered, False if not found
        """
        instance = self._instances.pop(agent_id, None)
        if instance:
            # Remove from user instances
            if instance.created_by and instance.created_by in self._user_instances:
                user_instances = self._user_instances[instance.created_by]
                if agent_id in user_instances:
                    user_instances.remove(agent_id)
                
                # Clean up empty user entries
                if not user_instances:
                    del self._user_instances[instance.created_by]
            
            logger.info(f"Unregistered agent instance: {agent_id}")
            return True
        
        return False
    
    def list_agents(
        self,
        user_id: Optional[str] = None,
        agent_type: Optional[str] = None,
        status: Optional[AgentStatus] = None
    ) -> List[AgentInstance]:
        """
        List agent instances with optional filtering.
        
        Args:
            user_id: Filter by user ID
            agent_type: Filter by agent type
            status: Filter by agent status
            
        Returns:
            List[AgentInstance]: Filtered agent instances
        """
        instances = list(self._instances.values())
        
        # Apply filters
        if user_id:
            instances = [i for i in instances if i.created_by == user_id]
        
        if agent_type:
            instances = [i for i in instances if i.agent_type == agent_type]
        
        if status:
            instances = [i for i in instances if i.status == status]
        
        return instances
    
    def get_user_agents(self, user_id: str) -> List[AgentInstance]:
        """
        Get all agent instances for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List[AgentInstance]: User's agent instances
        """
        return self.list_agents(user_id=user_id)
    
    def update_agent_status(self, agent_id: str, status: AgentStatus) -> bool:
        """
        Update agent status.
        
        Args:
            agent_id: Agent instance ID
            status: New status
            
        Returns:
            bool: True if updated, False if not found
        """
        instance = self._instances.get(agent_id)
        if instance:
            instance.set_status(status)
            return True
        return False
    
    def increment_execution_count(self, agent_id: str) -> bool:
        """
        Increment execution count for an agent.
        
        Args:
            agent_id: Agent instance ID
            
        Returns:
            bool: True if updated, False if not found
        """
        instance = self._instances.get(agent_id)
        if instance:
            instance.increment_execution_count()
            return True
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get registry statistics.
        
        Returns:
            Dict[str, Any]: Registry statistics
        """
        total_instances = len(self._instances)
        
        # Count by type
        type_counts = {}
        status_counts = {}
        
        for instance in self._instances.values():
            # Count by type
            type_counts[instance.agent_type] = type_counts.get(instance.agent_type, 0) + 1
            
            # Count by status
            status_counts[instance.status] = status_counts.get(instance.status, 0) + 1
        
        return {
            "total_instances": total_instances,
            "max_instances": self.max_instances,
            "type_counts": type_counts,
            "status_counts": status_counts,
            "user_count": len(self._user_instances)
        }
    
    def _cleanup_old_instances(self, max_age_hours: int = 24) -> int:
        """
        Clean up old unused agent instances.
        
        Args:
            max_age_hours: Maximum age in hours for unused instances
            
        Returns:
            int: Number of instances cleaned up
        """
        from datetime import timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        instances_to_remove = []
        
        for agent_id, instance in self._instances.items():
            # Remove old idle instances
            if (instance.status == AgentStatus.IDLE and 
                instance.last_used_at < cutoff_time):
                instances_to_remove.append(agent_id)
        
        # Remove identified instances
        for agent_id in instances_to_remove:
            self.unregister_agent(agent_id)
        
        if instances_to_remove:
            logger.info(f"Cleaned up {len(instances_to_remove)} old agent instances")
        
        return len(instances_to_remove)
    
    def clear_user_agents(self, user_id: str) -> int:
        """
        Clear all agent instances for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            int: Number of instances removed
        """
        user_agent_ids = self._user_instances.get(user_id, []).copy()
        
        for agent_id in user_agent_ids:
            self.unregister_agent(agent_id)
        
        return len(user_agent_ids)
    
    def clear_all(self) -> int:
        """
        Clear all agent instances.
        
        Returns:
            int: Number of instances removed
        """
        count = len(self._instances)
        self._instances.clear()
        self._user_instances.clear()
        
        logger.info(f"Cleared all {count} agent instances")
        return count


# Global agent registry instance
agent_registry = AgentRegistry()
