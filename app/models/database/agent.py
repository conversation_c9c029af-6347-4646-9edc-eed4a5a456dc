"""
Agent Database Models

This module contains SQLAlchemy models for agent-related entities.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy import <PERSON>umn, DateTime, Foreign<PERSON>ey, Integer, String, Text, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.constants import AgentStatus, AgentType
from app.models.database.base import Base


class Agent(Base):
    """
    Agent model representing an AI agent configuration.
    
    Stores agent metadata, configuration, and status information.
    """
    
    __tablename__ = "agents"
    
    # Agent identification
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        comment="Agent name"
    )
    
    type: Mapped[AgentType] = mapped_column(
        String(50),
        nullable=False,
        index=True,
        comment="Agent type (v5_agent, deep_research, task_planner)"
    )
    
    version: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="1.0.0",
        comment="Agent version"
    )
    
    # Agent description and metadata
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Agent description"
    )
    
    # Agent configuration
    config: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="Agent configuration as JSON"
    )
    
    # Agent status
    status: Mapped[AgentStatus] = mapped_column(
        String(50),
        nullable=False,
        default=AgentStatus.IDLE,
        index=True,
        comment="Current agent status"
    )
    
    # Agent capabilities
    capabilities: Mapped[list[str]] = mapped_column(
        JSON,
        nullable=False,
        default=list,
        comment="List of agent capabilities"
    )
    
    # Agent tools
    tools: Mapped[list[str]] = mapped_column(
        JSON,
        nullable=False,
        default=list,
        comment="List of available tools"
    )
    
    # Performance metrics
    total_executions: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Total number of executions"
    )
    
    successful_executions: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Number of successful executions"
    )
    
    failed_executions: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Number of failed executions"
    )
    
    average_execution_time: Mapped[Optional[float]] = mapped_column(
        nullable=True,
        comment="Average execution time in seconds"
    )
    
    # Relationships
    executions = relationship(
        "AgentExecution",
        back_populates="agent",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        """String representation of agent."""
        return f"<Agent(id={self.id}, name={self.name}, type={self.type})>"


class AgentExecution(Base):
    """
    Agent execution model representing a single agent run.
    
    Stores execution details, status, and results.
    """
    
    __tablename__ = "agent_executions"
    
    # Foreign key to agent
    agent_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("agents.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="Reference to agent"
    )
    
    # Execution identification
    execution_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        unique=True,
        index=True,
        comment="Unique execution identifier"
    )
    
    # Execution status
    status: Mapped[AgentStatus] = mapped_column(
        String(50),
        nullable=False,
        default=AgentStatus.RUNNING,
        index=True,
        comment="Execution status"
    )
    
    # Execution timing
    started_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        default=datetime.utcnow,
        comment="Execution start time"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="Execution completion time"
    )
    
    duration_seconds: Mapped[Optional[float]] = mapped_column(
        nullable=True,
        comment="Execution duration in seconds"
    )
    
    # Execution input and output
    input_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="Execution input data"
    )
    
    output_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="Execution output data"
    )
    
    # Error information
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Error message if execution failed"
    )
    
    error_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="Detailed error information"
    )
    
    # Execution metadata
    execution_metadata: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="Additional execution metadata"
    )
    
    # Performance metrics
    iterations_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Number of iterations executed"
    )
    
    tokens_used: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="Total tokens used in LLM calls"
    )
    
    cost_estimate: Mapped[Optional[float]] = mapped_column(
        nullable=True,
        comment="Estimated cost of execution"
    )
    
    # Relationships
    agent = relationship("Agent", back_populates="executions")
    
    def __repr__(self) -> str:
        """String representation of agent execution."""
        return f"<AgentExecution(id={self.id}, execution_id={self.execution_id}, status={self.status})>"
    
    @property
    def is_running(self) -> bool:
        """Check if execution is currently running."""
        return self.status == AgentStatus.RUNNING
    
    @property
    def is_completed(self) -> bool:
        """Check if execution is completed."""
        return self.status in [AgentStatus.COMPLETED, AgentStatus.FAILED, AgentStatus.CANCELLED]
    
    def calculate_duration(self) -> Optional[float]:
        """Calculate execution duration."""
        if self.completed_at and self.started_at:
            delta = self.completed_at - self.started_at
            return delta.total_seconds()
        return None
