"""
Conversation Database Models

This module contains SQLAlchemy models for conversation-related entities.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Foreign<PERSON>ey, Integer, String, Text, JSON, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.constants import ConversationStatus, MessageType
from app.models.database.base import Base


class Conversation(Base):
    """
    Conversation model representing a chat session.
    
    Stores conversation metadata, participants, and configuration.
    """
    
    __tablename__ = "conversations"
    
    # Conversation identification
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Conversation title"
    )
    
    # Conversation status
    status: Mapped[ConversationStatus] = mapped_column(
        String(50),
        nullable=False,
        default=ConversationStatus.ACTIVE,
        index=True,
        comment="Conversation status"
    )
    
    # Participant information
    user_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True,
        comment="User identifier"
    )
    
    # Agent configuration
    agent_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="Type of agent used in conversation"
    )
    
    agent_config: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="Agent configuration for this conversation"
    )
    
    # Conversation metadata
    conversation_metadata: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="Additional conversation metadata"
    )
    
    # Conversation settings
    settings: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="Conversation settings and preferences"
    )
    
    # Performance metrics
    message_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Total number of messages"
    )
    
    total_tokens: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="Total tokens used in conversation"
    )
    
    # Timing information
    last_activity_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        default=datetime.utcnow,
        comment="Last activity timestamp"
    )
    
    # Relationships
    messages = relationship(
        "Message",
        back_populates="conversation",
        cascade="all, delete-orphan",
        order_by="Message.created_at"
    )
    
    def __repr__(self) -> str:
        """String representation of conversation."""
        return f"<Conversation(id={self.id}, title={self.title}, status={self.status})>"
    
    @property
    def is_active(self) -> bool:
        """Check if conversation is active."""
        return self.status == ConversationStatus.ACTIVE
    
    def update_last_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity_at = datetime.utcnow()


class Message(Base):
    """
    Message model representing a single message in a conversation.
    
    Stores message content, metadata, and relationships.
    """
    
    __tablename__ = "messages"
    
    # Foreign key to conversation
    conversation_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("conversations.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="Reference to conversation"
    )
    
    # Message identification
    message_type: Mapped[MessageType] = mapped_column(
        String(50),
        nullable=False,
        index=True,
        comment="Type of message (user, assistant, system, tool)"
    )
    
    # Message content
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Message content"
    )
    
    # Message metadata
    message_metadata: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="Message metadata"
    )
    
    # Tool-specific fields
    tool_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="Name of tool used (for tool messages)"
    )
    
    tool_input: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="Tool input parameters"
    )
    
    tool_output: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="Tool output result"
    )
    
    # LLM-specific fields
    model_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="LLM model used for generation"
    )
    
    tokens_used: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="Tokens used for this message"
    )
    
    # Message ordering
    sequence_number: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        comment="Message sequence number in conversation"
    )
    
    # Message status
    is_edited: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        comment="Whether message has been edited"
    )
    
    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        comment="Whether message has been deleted"
    )
    
    # Parent message for threading
    parent_message_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("messages.id", ondelete="SET NULL"),
        nullable=True,
        comment="Reference to parent message"
    )
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    parent_message = relationship("Message", remote_side="Message.id")
    
    def __repr__(self) -> str:
        """String representation of message."""
        return f"<Message(id={self.id}, type={self.message_type}, sequence={self.sequence_number})>"
    
    @property
    def is_user_message(self) -> bool:
        """Check if message is from user."""
        return self.message_type == MessageType.USER
    
    @property
    def is_assistant_message(self) -> bool:
        """Check if message is from assistant."""
        return self.message_type == MessageType.ASSISTANT
    
    @property
    def is_tool_message(self) -> bool:
        """Check if message is from tool."""
        return self.message_type == MessageType.TOOL
    
    @property
    def is_system_message(self) -> bool:
        """Check if message is system message."""
        return self.message_type == MessageType.SYSTEM
