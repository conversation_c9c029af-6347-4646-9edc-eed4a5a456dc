"""
API Response Schemas

This module contains Pydantic models for standardized API responses.
"""

import uuid
from datetime import datetime
from typing import Any, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

# Generic type for data payload
T = TypeVar('T')


class APIResponse(BaseModel, Generic[T]):
    """
    Standardized API response format.
    
    Provides consistent structure for all API responses with success status,
    data payload, error information, and metadata.
    """
    
    success: bool = Field(
        description="Indicates if the request was successful"
    )
    
    data: Optional[T] = Field(
        default=None,
        description="Response data payload"
    )
    
    message: str = Field(
        default="",
        description="Human-readable message"
    )
    
    error_code: Optional[str] = Field(
        default=None,
        description="Machine-readable error code"
    )
    
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        description="Response timestamp"
    )
    
    trace_id: str = Field(
        default="",
        description="Request trace ID for debugging"
    )
    
    @classmethod
    def success_response(
        cls,
        data: Optional[T] = None,
        message: str = "Success",
        trace_id: str = ""
    ) -> "APIResponse[T]":
        """
        Create a successful response.
        
        Args:
            data: Response data
            message: Success message
            trace_id: Request trace ID
            
        Returns:
            APIResponse: Success response
        """
        return cls(
            success=True,
            data=data,
            message=message,
            trace_id=trace_id
        )
    
    @classmethod
    def error_response(
        cls,
        message: str,
        error_code: Optional[str] = None,
        trace_id: str = ""
    ) -> "APIResponse[None]":
        """
        Create an error response.
        
        Args:
            message: Error message
            error_code: Error code
            trace_id: Request trace ID
            
        Returns:
            APIResponse: Error response
        """
        return cls(
            success=False,
            data=None,
            message=message,
            error_code=error_code,
            trace_id=trace_id
        )


class PaginatedResponse(BaseModel, Generic[T]):
    """
    Paginated response format for list endpoints.
    
    Provides pagination metadata along with the data items.
    """
    
    data: List[T] = Field(
        default_factory=list,
        description="List of data items"
    )
    
    total: int = Field(
        default=0,
        description="Total number of items"
    )
    
    page: int = Field(
        default=1,
        description="Current page number"
    )
    
    page_size: int = Field(
        default=20,
        description="Number of items per page"
    )
    
    total_pages: int = Field(
        default=0,
        description="Total number of pages"
    )
    
    has_next: bool = Field(
        default=False,
        description="Whether there is a next page"
    )
    
    has_prev: bool = Field(
        default=False,
        description="Whether there is a previous page"
    )
    
    @classmethod
    def create(
        cls,
        data: List[T],
        total: int,
        page: int,
        page_size: int
    ) -> "PaginatedResponse[T]":
        """
        Create a paginated response.
        
        Args:
            data: List of data items
            total: Total number of items
            page: Current page number
            page_size: Number of items per page
            
        Returns:
            PaginatedResponse: Paginated response
        """
        total_pages = (total + page_size - 1) // page_size if total > 0 else 0
        
        return cls(
            data=data,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )


class HealthCheckResponse(BaseModel):
    """Health check response schema."""
    
    status: str = Field(description="Service status")
    version: str = Field(description="Application version")
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        description="Health check timestamp"
    )
    services: dict[str, str] = Field(
        default_factory=dict,
        description="Status of dependent services"
    )


class ErrorDetail(BaseModel):
    """Detailed error information."""
    
    field: Optional[str] = Field(
        default=None,
        description="Field that caused the error"
    )
    
    message: str = Field(
        description="Error message"
    )
    
    code: Optional[str] = Field(
        default=None,
        description="Error code"
    )


class ValidationErrorResponse(BaseModel):
    """Validation error response schema."""
    
    success: bool = Field(default=False)
    message: str = Field(description="General error message")
    error_code: str = Field(default="VALIDATION_ERROR")
    errors: List[ErrorDetail] = Field(
        description="List of validation errors"
    )
    timestamp: datetime = Field(
        default_factory=datetime.utcnow
    )


class MetricsResponse(BaseModel):
    """Metrics response schema."""
    
    metrics: dict[str, Any] = Field(
        description="Application metrics"
    )
    
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        description="Metrics timestamp"
    )


class StatusResponse(BaseModel):
    """General status response schema."""
    
    id: uuid.UUID = Field(description="Resource ID")
    status: str = Field(description="Resource status")
    message: Optional[str] = Field(
        default=None,
        description="Status message"
    )
    updated_at: datetime = Field(
        description="Last update timestamp"
    )
