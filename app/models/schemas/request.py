"""
API Request Schemas

This module contains Pydantic models for API request validation.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class BaseRequest(BaseModel):
    """
    Base request schema with common validation.
    
    Provides common functionality for all request schemas.
    """
    
    class Config:
        """Pydantic configuration."""
        # Allow extra fields to be ignored
        extra = "ignore"
        # Use enum values instead of enum objects
        use_enum_values = True
        # Validate assignment
        validate_assignment = True


class PaginationRequest(BaseRequest):
    """
    Pagination request parameters.
    
    Used for list endpoints that support pagination.
    """
    
    page: int = Field(
        default=1,
        ge=1,
        description="Page number (1-based)"
    )
    
    page_size: int = Field(
        default=20,
        ge=1,
        le=100,
        description="Number of items per page"
    )
    
    sort_by: Optional[str] = Field(
        default=None,
        description="Field to sort by"
    )
    
    sort_order: Optional[str] = Field(
        default="asc",
        description="Sort order (asc/desc)"
    )
    
    @validator("sort_order")
    def validate_sort_order(cls, v: Optional[str]) -> Optional[str]:
        """Validate sort order."""
        if v is not None and v.lower() not in ["asc", "desc"]:
            raise ValueError("sort_order must be 'asc' or 'desc'")
        return v.lower() if v else v


class SearchRequest(PaginationRequest):
    """
    Search request parameters.
    
    Extends pagination with search functionality.
    """
    
    query: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=500,
        description="Search query"
    )
    
    filters: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional search filters"
    )


class BulkOperationRequest(BaseRequest):
    """
    Bulk operation request schema.
    
    Used for operations that affect multiple resources.
    """
    
    ids: List[str] = Field(
        min_items=1,
        max_items=100,
        description="List of resource IDs"
    )
    
    operation: str = Field(
        description="Operation to perform"
    )
    
    parameters: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Operation parameters"
    )


class FileUploadRequest(BaseRequest):
    """
    File upload request schema.
    
    Used for file upload endpoints.
    """
    
    filename: str = Field(
        min_length=1,
        max_length=255,
        description="Original filename"
    )
    
    content_type: str = Field(
        description="File MIME type"
    )
    
    size: int = Field(
        ge=1,
        description="File size in bytes"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional file metadata"
    )
    
    @validator("size")
    def validate_file_size(cls, v: int) -> int:
        """Validate file size."""
        max_size = 10 * 1024 * 1024  # 10MB
        if v > max_size:
            raise ValueError(f"File size cannot exceed {max_size} bytes")
        return v


class ConfigurationRequest(BaseRequest):
    """
    Configuration update request schema.
    
    Used for updating application configuration.
    """
    
    key: str = Field(
        min_length=1,
        max_length=255,
        description="Configuration key"
    )
    
    value: Any = Field(
        description="Configuration value"
    )
    
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="Configuration description"
    )


class HealthCheckRequest(BaseRequest):
    """
    Health check request schema.
    
    Used for health check endpoints.
    """
    
    include_services: bool = Field(
        default=True,
        description="Include dependent services in health check"
    )
    
    timeout: Optional[int] = Field(
        default=5,
        ge=1,
        le=30,
        description="Health check timeout in seconds"
    )
