"""
Conversation Schemas

This module contains Pydantic models for conversation-related API operations.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator

from app.core.constants import AgentType, ConversationStatus, MessageType
from app.models.schemas.request import BaseRequest


class MessageCreate(BaseRequest):
    """Schema for creating a new message."""
    
    content: str = Field(
        min_length=1,
        max_length=10000,
        description="Message content"
    )
    
    message_type: MessageType = Field(
        default=MessageType.USER,
        description="Type of message"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional message metadata"
    )
    
    parent_message_id: Optional[uuid.UUID] = Field(
        default=None,
        description="ID of parent message for threading"
    )


class MessageUpdate(BaseRequest):
    """Schema for updating a message."""
    
    content: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=10000,
        description="Updated message content"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Updated message metadata"
    )


class MessageResponse(BaseModel):
    """Schema for message response."""
    
    id: uuid.UUID = Field(description="Message ID")
    conversation_id: uuid.UUID = Field(description="Conversation ID")
    message_type: MessageType = Field(description="Message type")
    content: str = Field(description="Message content")
    metadata: Dict[str, Any] = Field(description="Message metadata")
    sequence_number: int = Field(description="Message sequence number")
    
    # Tool-specific fields
    tool_name: Optional[str] = Field(
        default=None,
        description="Tool name for tool messages"
    )
    tool_input: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Tool input parameters"
    )
    tool_output: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Tool output result"
    )
    
    # LLM-specific fields
    model_name: Optional[str] = Field(
        default=None,
        description="LLM model used"
    )
    tokens_used: Optional[int] = Field(
        default=None,
        description="Tokens used for this message"
    )
    
    # Status fields
    is_edited: bool = Field(description="Whether message was edited")
    is_deleted: bool = Field(description="Whether message was deleted")
    
    # Timestamps
    created_at: datetime = Field(description="Creation timestamp")
    updated_at: datetime = Field(description="Last update timestamp")
    
    # Parent message
    parent_message_id: Optional[uuid.UUID] = Field(
        default=None,
        description="Parent message ID"
    )
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class ConversationCreate(BaseRequest):
    """Schema for creating a new conversation."""
    
    title: str = Field(
        min_length=1,
        max_length=255,
        description="Conversation title"
    )
    
    agent_type: AgentType = Field(
        default=AgentType.V5_AGENT,
        description="Type of agent to use"
    )
    
    agent_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Agent configuration"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional conversation metadata"
    )
    
    settings: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Conversation settings"
    )
    
    initial_message: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=10000,
        description="Initial message to start conversation"
    )


class ConversationUpdate(BaseRequest):
    """Schema for updating a conversation."""
    
    title: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=255,
        description="Updated conversation title"
    )
    
    status: Optional[ConversationStatus] = Field(
        default=None,
        description="Updated conversation status"
    )
    
    agent_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Updated agent configuration"
    )
    
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Updated conversation metadata"
    )
    
    settings: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Updated conversation settings"
    )


class ConversationResponse(BaseModel):
    """Schema for conversation response."""
    
    id: uuid.UUID = Field(description="Conversation ID")
    title: str = Field(description="Conversation title")
    status: ConversationStatus = Field(description="Conversation status")
    user_id: Optional[str] = Field(description="User ID")
    
    # Agent information
    agent_type: str = Field(description="Agent type")
    agent_config: Dict[str, Any] = Field(description="Agent configuration")
    
    # Metadata
    metadata: Dict[str, Any] = Field(description="Conversation metadata")
    settings: Dict[str, Any] = Field(description="Conversation settings")
    
    # Statistics
    message_count: int = Field(description="Number of messages")
    total_tokens: int = Field(description="Total tokens used")
    
    # Timestamps
    created_at: datetime = Field(description="Creation timestamp")
    updated_at: datetime = Field(description="Last update timestamp")
    last_activity_at: datetime = Field(description="Last activity timestamp")
    
    # Recent messages (optional)
    recent_messages: Optional[List[MessageResponse]] = Field(
        default=None,
        description="Recent messages in conversation"
    )
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class ConversationSummary(BaseModel):
    """Schema for conversation summary."""
    
    id: uuid.UUID = Field(description="Conversation ID")
    title: str = Field(description="Conversation title")
    status: ConversationStatus = Field(description="Conversation status")
    agent_type: str = Field(description="Agent type")
    message_count: int = Field(description="Number of messages")
    last_activity_at: datetime = Field(description="Last activity timestamp")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class ChatRequest(BaseRequest):
    """Schema for chat request (send message and get response)."""
    
    message: str = Field(
        min_length=1,
        max_length=10000,
        description="User message"
    )
    
    conversation_id: Optional[uuid.UUID] = Field(
        default=None,
        description="Existing conversation ID (optional for new conversation)"
    )
    
    agent_type: Optional[AgentType] = Field(
        default=None,
        description="Agent type (for new conversation)"
    )
    
    agent_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Agent configuration (for new conversation)"
    )
    
    stream: bool = Field(
        default=False,
        description="Whether to stream the response"
    )


class ChatResponse(BaseModel):
    """Schema for chat response."""
    
    conversation_id: uuid.UUID = Field(description="Conversation ID")
    user_message: MessageResponse = Field(description="User message")
    assistant_message: MessageResponse = Field(description="Assistant response")
    
    # Execution metadata
    execution_time: float = Field(description="Execution time in seconds")
    tokens_used: int = Field(description="Total tokens used")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
