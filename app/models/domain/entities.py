"""
Domain Entities

This module contains domain entities that encapsulate business logic
and represent core business concepts.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from app.core.constants import AgentStatus, AgentType, ConversationStatus, MessageType


class BaseEntity(BaseModel):
    """
    Base domain entity with common fields and behavior.
    
    All domain entities inherit from this base class.
    """
    
    id: uuid.UUID = Field(description="Entity unique identifier")
    created_at: datetime = Field(description="Creation timestamp")
    updated_at: datetime = Field(description="Last update timestamp")
    
    class Config:
        """Pydantic configuration."""
        # Allow arbitrary types for complex objects
        arbitrary_types_allowed = True
        # Use enum values
        use_enum_values = True


class UserEntity(BaseEntity):
    """
    User domain entity.
    
    Represents a user in the system with authentication and profile information.
    """
    
    username: str = Field(description="Unique username")
    email: str = Field(description="User email address")
    full_name: Optional[str] = Field(default=None, description="User full name")
    is_active: bool = Field(default=True, description="Whether user is active")
    is_admin: bool = Field(default=False, description="Whether user has admin privileges")
    
    # Profile information
    profile: Dict[str, Any] = Field(
        default_factory=dict,
        description="User profile data"
    )
    
    # Preferences
    preferences: Dict[str, Any] = Field(
        default_factory=dict,
        description="User preferences"
    )
    
    # Statistics
    total_conversations: int = Field(default=0, description="Total conversations")
    total_messages: int = Field(default=0, description="Total messages sent")
    
    def can_create_conversation(self) -> bool:
        """Check if user can create new conversations."""
        return self.is_active
    
    def can_access_conversation(self, conversation_id: uuid.UUID) -> bool:
        """Check if user can access a specific conversation."""
        # TODO: Implement conversation access control logic
        return self.is_active
    
    def update_statistics(self, conversations: int = 0, messages: int = 0) -> None:
        """Update user statistics."""
        self.total_conversations += conversations
        self.total_messages += messages


class AgentEntity(BaseEntity):
    """
    Agent domain entity.
    
    Represents an AI agent with its configuration, capabilities, and state.
    """
    
    name: str = Field(description="Agent name")
    type: AgentType = Field(description="Agent type")
    version: str = Field(description="Agent version")
    description: Optional[str] = Field(default=None, description="Agent description")
    
    # Configuration
    config: Dict[str, Any] = Field(
        default_factory=dict,
        description="Agent configuration"
    )
    
    # Capabilities
    capabilities: List[str] = Field(
        default_factory=list,
        description="Agent capabilities"
    )
    
    tools: List[str] = Field(
        default_factory=list,
        description="Available tools"
    )
    
    # Status
    status: AgentStatus = Field(
        default=AgentStatus.IDLE,
        description="Current agent status"
    )
    
    # Performance metrics
    total_executions: int = Field(default=0, description="Total executions")
    successful_executions: int = Field(default=0, description="Successful executions")
    failed_executions: int = Field(default=0, description="Failed executions")
    average_execution_time: Optional[float] = Field(
        default=None,
        description="Average execution time"
    )
    
    @property
    def success_rate(self) -> float:
        """Calculate agent success rate."""
        if self.total_executions == 0:
            return 0.0
        return self.successful_executions / self.total_executions
    
    def can_execute(self) -> bool:
        """Check if agent can execute tasks."""
        return self.status in [AgentStatus.IDLE, AgentStatus.RUNNING]
    
    def has_capability(self, capability: str) -> bool:
        """Check if agent has a specific capability."""
        return capability in self.capabilities
    
    def has_tool(self, tool_name: str) -> bool:
        """Check if agent has access to a specific tool."""
        return tool_name in self.tools
    
    def update_execution_stats(
        self,
        success: bool,
        execution_time: Optional[float] = None
    ) -> None:
        """Update execution statistics."""
        self.total_executions += 1
        
        if success:
            self.successful_executions += 1
        else:
            self.failed_executions += 1
        
        if execution_time is not None:
            if self.average_execution_time is None:
                self.average_execution_time = execution_time
            else:
                # Calculate running average
                total_time = self.average_execution_time * (self.total_executions - 1)
                self.average_execution_time = (total_time + execution_time) / self.total_executions


class MessageEntity(BaseEntity):
    """
    Message domain entity.
    
    Represents a message in a conversation with content and metadata.
    """
    
    conversation_id: uuid.UUID = Field(description="Conversation ID")
    message_type: MessageType = Field(description="Message type")
    content: str = Field(description="Message content")
    sequence_number: int = Field(description="Message sequence number")
    
    # Metadata
    message_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Message metadata"
    )
    
    # Tool-specific fields
    tool_name: Optional[str] = Field(default=None, description="Tool name")
    tool_input: Optional[Dict[str, Any]] = Field(default=None, description="Tool input")
    tool_output: Optional[Dict[str, Any]] = Field(default=None, description="Tool output")
    
    # LLM-specific fields
    model_name: Optional[str] = Field(default=None, description="LLM model used")
    tokens_used: Optional[int] = Field(default=None, description="Tokens used")
    
    # Status
    is_edited: bool = Field(default=False, description="Whether message was edited")
    is_deleted: bool = Field(default=False, description="Whether message was deleted")
    
    # Threading
    parent_message_id: Optional[uuid.UUID] = Field(
        default=None,
        description="Parent message ID"
    )
    
    @property
    def is_user_message(self) -> bool:
        """Check if message is from user."""
        return self.message_type == MessageType.USER
    
    @property
    def is_assistant_message(self) -> bool:
        """Check if message is from assistant."""
        return self.message_type == MessageType.ASSISTANT
    
    @property
    def is_tool_message(self) -> bool:
        """Check if message is from tool."""
        return self.message_type == MessageType.TOOL
    
    @property
    def is_system_message(self) -> bool:
        """Check if message is system message."""
        return self.message_type == MessageType.SYSTEM
    
    def can_edit(self, user_id: Optional[str] = None) -> bool:
        """Check if message can be edited."""
        # Only user messages can be edited, and only if not deleted
        return self.is_user_message and not self.is_deleted
    
    def can_delete(self, user_id: Optional[str] = None) -> bool:
        """Check if message can be deleted."""
        # Messages can be deleted if not already deleted
        return not self.is_deleted


class ConversationEntity(BaseEntity):
    """
    Conversation domain entity.
    
    Represents a conversation session with messages and agent configuration.
    """
    
    title: str = Field(description="Conversation title")
    status: ConversationStatus = Field(description="Conversation status")
    user_id: Optional[str] = Field(default=None, description="User ID")
    
    # Agent configuration
    agent_type: str = Field(description="Agent type")
    agent_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="Agent configuration"
    )
    
    # Metadata
    conversation_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Conversation metadata"
    )
    
    settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Conversation settings"
    )
    
    # Statistics
    message_count: int = Field(default=0, description="Number of messages")
    total_tokens: int = Field(default=0, description="Total tokens used")
    
    # Timing
    last_activity_at: datetime = Field(description="Last activity timestamp")
    
    # Messages (loaded separately)
    messages: List[MessageEntity] = Field(
        default_factory=list,
        description="Conversation messages"
    )
    
    @property
    def is_active(self) -> bool:
        """Check if conversation is active."""
        return self.status == ConversationStatus.ACTIVE
    
    @property
    def is_archived(self) -> bool:
        """Check if conversation is archived."""
        return self.status == ConversationStatus.ARCHIVED
    
    @property
    def is_deleted(self) -> bool:
        """Check if conversation is deleted."""
        return self.status == ConversationStatus.DELETED
    
    def can_add_message(self) -> bool:
        """Check if messages can be added to conversation."""
        return self.is_active
    
    def can_edit(self, user_id: Optional[str] = None) -> bool:
        """Check if conversation can be edited."""
        return self.is_active and (user_id is None or self.user_id == user_id)
    
    def can_delete(self, user_id: Optional[str] = None) -> bool:
        """Check if conversation can be deleted."""
        return not self.is_deleted and (user_id is None or self.user_id == user_id)
    
    def add_message(self, message: MessageEntity) -> None:
        """Add a message to the conversation."""
        if not self.can_add_message():
            raise ValueError("Cannot add message to inactive conversation")
        
        message.sequence_number = len(self.messages) + 1
        self.messages.append(message)
        self.message_count += 1
        
        if message.tokens_used:
            self.total_tokens += message.tokens_used
        
        self.update_last_activity()
    
    def update_last_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity_at = datetime.utcnow()
    
    def archive(self) -> None:
        """Archive the conversation."""
        if self.is_active:
            self.status = ConversationStatus.ARCHIVED
    
    def restore(self) -> None:
        """Restore archived conversation."""
        if self.is_archived:
            self.status = ConversationStatus.ACTIVE
    
    def soft_delete(self) -> None:
        """Soft delete the conversation."""
        if not self.is_deleted:
            self.status = ConversationStatus.DELETED
