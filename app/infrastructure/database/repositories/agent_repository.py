"""
Agent Repository

This module contains the repository implementation for agent entities.
"""

import uuid
from typing import List, Optional, Tuple

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.infrastructure.database.repositories.base import BaseRepository
from app.models.database.agent import Agent, AgentExecution
from app.models.domain.entities import AgentEntity


class AgentRepository(BaseRepository[Agent]):
    """
    Repository for agent entities.
    
    Provides data access methods specific to agents,
    including type-based filtering and execution history.
    """
    
    def __init__(self, db_session: AsyncSession):
        """
        Initialize agent repository.
        
        Args:
            db_session: Database session
        """
        super().__init__(db_session, Agent)
    
    async def create(self, agent_entity: AgentEntity) -> AgentEntity:
        """
        Create a new agent from domain entity.
        
        Args:
            agent_entity: Agent domain entity
            
        Returns:
            AgentEntity: Created agent entity
        """
        # Convert domain entity to database model
        db_agent = Agent(
            id=agent_entity.id,
            name=agent_entity.name,
            type=agent_entity.type,
            version=agent_entity.version,
            description=agent_entity.description,
            config=agent_entity.config,
            status=agent_entity.status,
            capabilities=agent_entity.capabilities,
            tools=agent_entity.tools,
            total_executions=agent_entity.total_executions,
            successful_executions=agent_entity.successful_executions,
            failed_executions=agent_entity.failed_executions,
            average_execution_time=agent_entity.average_execution_time,
            created_at=agent_entity.created_at,
            updated_at=agent_entity.updated_at
        )
        
        # Save to database
        saved_agent = await super().create(db_agent)
        
        # Convert back to domain entity
        return self._to_domain_entity(saved_agent)
    
    async def get_by_id(self, agent_id: uuid.UUID) -> Optional[AgentEntity]:
        """
        Get agent by ID.
        
        Args:
            agent_id: Agent ID
            
        Returns:
            Optional[AgentEntity]: Agent entity or None
        """
        db_agent = await super().get_by_id(agent_id)
        if db_agent:
            return self._to_domain_entity(db_agent)
        return None
    
    async def get_by_name(self, name: str) -> Optional[AgentEntity]:
        """
        Get agent by name.
        
        Args:
            name: Agent name
            
        Returns:
            Optional[AgentEntity]: Agent entity or None
        """
        db_agent = await self.find_one_by_field("name", name)
        if db_agent:
            return self._to_domain_entity(db_agent)
        return None
    
    async def list_by_type(
        self,
        agent_type: str,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[AgentEntity], int]:
        """
        List agents by type with pagination.
        
        Args:
            agent_type: Agent type
            page: Page number (1-based)
            page_size: Number of items per page
            
        Returns:
            Tuple[List[AgentEntity], int]: Agents and total count
        """
        offset = (page - 1) * page_size
        
        # Build query
        query = select(Agent).where(Agent.type == agent_type)
        count_query = select(func.count()).where(Agent.type == agent_type)
        
        # Get total count
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and sorting
        query = (
            query
            .order_by(desc(Agent.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        # Execute query
        result = await self.db_session.execute(query)
        db_agents = list(result.scalars().all())
        
        # Convert to domain entities
        agents = [self._to_domain_entity(db_agent) for db_agent in db_agents]
        
        return agents, total
    
    async def list_available_agents(
        self,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[AgentEntity], int]:
        """
        List available (non-failed) agents.
        
        Args:
            page: Page number (1-based)
            page_size: Number of items per page
            
        Returns:
            Tuple[List[AgentEntity], int]: Available agents and total count
        """
        offset = (page - 1) * page_size
        
        # Build query for available agents
        query = select(Agent).where(Agent.status != "failed")
        count_query = select(func.count()).where(Agent.status != "failed")
        
        # Get total count
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and sorting
        query = (
            query
            .order_by(Agent.name)
            .offset(offset)
            .limit(page_size)
        )
        
        # Execute query
        result = await self.db_session.execute(query)
        db_agents = list(result.scalars().all())
        
        # Convert to domain entities
        agents = [self._to_domain_entity(db_agent) for db_agent in db_agents]
        
        return agents, total
    
    async def update(self, agent_entity: AgentEntity) -> AgentEntity:
        """
        Update agent from domain entity.
        
        Args:
            agent_entity: Updated agent entity
            
        Returns:
            AgentEntity: Updated agent entity
        """
        # Get existing database record
        db_agent = await super().get_by_id(agent_entity.id)
        if not db_agent:
            raise ValueError(f"Agent {agent_entity.id} not found")
        
        # Update fields
        db_agent.name = agent_entity.name
        db_agent.description = agent_entity.description
        db_agent.config = agent_entity.config
        db_agent.status = agent_entity.status
        db_agent.capabilities = agent_entity.capabilities
        db_agent.tools = agent_entity.tools
        db_agent.total_executions = agent_entity.total_executions
        db_agent.successful_executions = agent_entity.successful_executions
        db_agent.failed_executions = agent_entity.failed_executions
        db_agent.average_execution_time = agent_entity.average_execution_time
        db_agent.updated_at = agent_entity.updated_at
        
        # Save changes
        updated_agent = await super().update(db_agent)
        
        # Convert back to domain entity
        return self._to_domain_entity(updated_agent)
    
    async def search_agents(
        self,
        query: str,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[AgentEntity], int]:
        """
        Search agents by name or description.
        
        Args:
            query: Search query
            page: Page number
            page_size: Number of items per page
            
        Returns:
            Tuple[List[AgentEntity], int]: Matching agents and total count
        """
        offset = (page - 1) * page_size
        
        # Build search query
        search_query = select(Agent).where(
            Agent.name.ilike(f"%{query}%") |
            Agent.description.ilike(f"%{query}%")
        )
        
        count_query = select(func.count()).where(
            Agent.name.ilike(f"%{query}%") |
            Agent.description.ilike(f"%{query}%")
        )
        
        # Get total count
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and sorting
        search_query = (
            search_query
            .order_by(Agent.name)
            .offset(offset)
            .limit(page_size)
        )
        
        # Execute query
        result = await self.db_session.execute(search_query)
        db_agents = list(result.scalars().all())
        
        # Convert to domain entities
        agents = [self._to_domain_entity(db_agent) for db_agent in db_agents]
        
        return agents, total
    
    async def get_agents_by_capability(
        self,
        capability: str
    ) -> List[AgentEntity]:
        """
        Get agents that have a specific capability.
        
        Args:
            capability: Capability to search for
            
        Returns:
            List[AgentEntity]: Agents with the capability
        """
        # PostgreSQL JSON array contains operator
        result = await self.db_session.execute(
            select(Agent).where(
                Agent.capabilities.op('@>')([capability])
            )
        )
        
        db_agents = list(result.scalars().all())
        return [self._to_domain_entity(db_agent) for db_agent in db_agents]
    
    async def get_agents_with_tool(
        self,
        tool_name: str
    ) -> List[AgentEntity]:
        """
        Get agents that have access to a specific tool.
        
        Args:
            tool_name: Tool name to search for
            
        Returns:
            List[AgentEntity]: Agents with the tool
        """
        # PostgreSQL JSON array contains operator
        result = await self.db_session.execute(
            select(Agent).where(
                Agent.tools.op('@>')([tool_name])
            )
        )
        
        db_agents = list(result.scalars().all())
        return [self._to_domain_entity(db_agent) for db_agent in db_agents]
    
    def _to_domain_entity(self, db_agent: Agent) -> AgentEntity:
        """
        Convert database model to domain entity.
        
        Args:
            db_agent: Database agent model
            
        Returns:
            AgentEntity: Domain agent entity
        """
        return AgentEntity(
            id=db_agent.id,
            name=db_agent.name,
            type=db_agent.type,
            version=db_agent.version,
            description=db_agent.description,
            config=db_agent.config,
            status=db_agent.status,
            capabilities=db_agent.capabilities,
            tools=db_agent.tools,
            total_executions=db_agent.total_executions,
            successful_executions=db_agent.successful_executions,
            failed_executions=db_agent.failed_executions,
            average_execution_time=db_agent.average_execution_time,
            created_at=db_agent.created_at,
            updated_at=db_agent.updated_at
        )
