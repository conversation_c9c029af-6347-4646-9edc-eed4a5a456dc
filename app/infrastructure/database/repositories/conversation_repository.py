"""
Conversation Repository

This module contains the repository implementation for conversation entities.
"""

import uuid
from typing import List, Optional, Tuple

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.infrastructure.database.repositories.base import BaseRepository
from app.models.database.conversation import Conversation, Message
from app.models.domain.entities import ConversationEntity


class ConversationRepository(BaseRepository[Conversation]):
    """
    Repository for conversation entities.
    
    Provides data access methods specific to conversations,
    including user-based filtering and message loading.
    """
    
    def __init__(self, db_session: AsyncSession):
        """
        Initialize conversation repository.
        
        Args:
            db_session: Database session
        """
        super().__init__(db_session, Conversation)
    
    async def create(self, conversation_entity: ConversationEntity) -> ConversationEntity:
        """
        Create a new conversation from domain entity.
        
        Args:
            conversation_entity: Conversation domain entity
            
        Returns:
            ConversationEntity: Created conversation entity
        """
        # Convert domain entity to database model
        db_conversation = Conversation(
            id=conversation_entity.id,
            title=conversation_entity.title,
            status=conversation_entity.status,
            user_id=conversation_entity.user_id,
            agent_type=conversation_entity.agent_type,
            agent_config=conversation_entity.agent_config,
            metadata=conversation_entity.metadata,
            settings=conversation_entity.settings,
            message_count=conversation_entity.message_count,
            total_tokens=conversation_entity.total_tokens,
            last_activity_at=conversation_entity.last_activity_at,
            created_at=conversation_entity.created_at,
            updated_at=conversation_entity.updated_at
        )
        
        # Save to database
        saved_conversation = await super().create(db_conversation)
        
        # Convert back to domain entity
        return self._to_domain_entity(saved_conversation)
    
    async def get_by_id(self, conversation_id: uuid.UUID) -> Optional[ConversationEntity]:
        """
        Get conversation by ID with messages.
        
        Args:
            conversation_id: Conversation ID
            
        Returns:
            Optional[ConversationEntity]: Conversation entity or None
        """
        result = await self.db_session.execute(
            select(Conversation)
            .options(selectinload(Conversation.messages))
            .where(Conversation.id == conversation_id)
        )
        
        db_conversation = result.scalar_one_or_none()
        if db_conversation:
            return self._to_domain_entity(db_conversation)
        return None
    
    async def list_by_user(
        self,
        user_id: str,
        page: int = 1,
        page_size: int = 20,
        sort_by: Optional[str] = None,
        sort_order: str = "desc",
        status_filter: Optional[str] = None
    ) -> Tuple[List[ConversationEntity], int]:
        """
        List conversations for a specific user with pagination.
        
        Args:
            user_id: User ID
            page: Page number (1-based)
            page_size: Number of items per page
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)
            status_filter: Optional status filter
            
        Returns:
            Tuple[List[ConversationEntity], int]: Conversations and total count
        """
        # Calculate offset
        offset = (page - 1) * page_size
        
        # Build base query
        query = select(Conversation).where(Conversation.user_id == user_id)
        count_query = select(func.count()).where(Conversation.user_id == user_id)
        
        # Add status filter
        if status_filter:
            query = query.where(Conversation.status == status_filter)
            count_query = count_query.where(Conversation.status == status_filter)
        
        # Add sorting
        if sort_by and hasattr(Conversation, sort_by):
            sort_column = getattr(Conversation, sort_by)
            if sort_order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(sort_column)
        else:
            # Default sort by last_activity_at desc
            query = query.order_by(desc(Conversation.last_activity_at))
        
        # Get total count
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination
        query = query.offset(offset).limit(page_size)
        
        # Execute query
        result = await self.db_session.execute(query)
        db_conversations = list(result.scalars().all())
        
        # Convert to domain entities
        conversations = [
            self._to_domain_entity(db_conv) for db_conv in db_conversations
        ]
        
        return conversations, total
    
    async def update(self, conversation_entity: ConversationEntity) -> ConversationEntity:
        """
        Update conversation from domain entity.
        
        Args:
            conversation_entity: Updated conversation entity
            
        Returns:
            ConversationEntity: Updated conversation entity
        """
        # Get existing database record
        db_conversation = await super().get_by_id(conversation_entity.id)
        if not db_conversation:
            raise ValueError(f"Conversation {conversation_entity.id} not found")
        
        # Update fields
        db_conversation.title = conversation_entity.title
        db_conversation.status = conversation_entity.status
        db_conversation.agent_config = conversation_entity.agent_config
        db_conversation.metadata = conversation_entity.metadata
        db_conversation.settings = conversation_entity.settings
        db_conversation.message_count = conversation_entity.message_count
        db_conversation.total_tokens = conversation_entity.total_tokens
        db_conversation.last_activity_at = conversation_entity.last_activity_at
        db_conversation.updated_at = conversation_entity.updated_at
        
        # Save changes
        updated_conversation = await super().update(db_conversation)
        
        # Convert back to domain entity
        return self._to_domain_entity(updated_conversation)
    
    async def get_recent_conversations(
        self,
        user_id: str,
        limit: int = 10
    ) -> List[ConversationEntity]:
        """
        Get recent conversations for a user.
        
        Args:
            user_id: User ID
            limit: Maximum number of conversations
            
        Returns:
            List[ConversationEntity]: Recent conversations
        """
        result = await self.db_session.execute(
            select(Conversation)
            .where(
                and_(
                    Conversation.user_id == user_id,
                    Conversation.status == "active"
                )
            )
            .order_by(desc(Conversation.last_activity_at))
            .limit(limit)
        )
        
        db_conversations = list(result.scalars().all())
        return [self._to_domain_entity(db_conv) for db_conv in db_conversations]
    
    async def search_conversations(
        self,
        user_id: str,
        query: str,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[ConversationEntity], int]:
        """
        Search conversations by title or content.
        
        Args:
            user_id: User ID
            query: Search query
            page: Page number
            page_size: Number of items per page
            
        Returns:
            Tuple[List[ConversationEntity], int]: Matching conversations and total count
        """
        offset = (page - 1) * page_size
        
        # Build search query (PostgreSQL full-text search)
        search_query = select(Conversation).where(
            and_(
                Conversation.user_id == user_id,
                Conversation.title.ilike(f"%{query}%")  # Simple LIKE search
            )
        )
        
        count_query = select(func.count()).where(
            and_(
                Conversation.user_id == user_id,
                Conversation.title.ilike(f"%{query}%")
            )
        )
        
        # Get total count
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination and sorting
        search_query = (
            search_query
            .order_by(desc(Conversation.last_activity_at))
            .offset(offset)
            .limit(page_size)
        )
        
        # Execute query
        result = await self.db_session.execute(search_query)
        db_conversations = list(result.scalars().all())
        
        # Convert to domain entities
        conversations = [
            self._to_domain_entity(db_conv) for db_conv in db_conversations
        ]
        
        return conversations, total
    
    def _to_domain_entity(self, db_conversation: Conversation) -> ConversationEntity:
        """
        Convert database model to domain entity.
        
        Args:
            db_conversation: Database conversation model
            
        Returns:
            ConversationEntity: Domain conversation entity
        """
        # TODO: Convert messages if loaded
        messages = []
        if hasattr(db_conversation, 'messages') and db_conversation.messages:
            # Convert messages to domain entities
            pass
        
        return ConversationEntity(
            id=db_conversation.id,
            title=db_conversation.title,
            status=db_conversation.status,
            user_id=db_conversation.user_id,
            agent_type=db_conversation.agent_type,
            agent_config=db_conversation.agent_config,
            metadata=db_conversation.metadata,
            settings=db_conversation.settings,
            message_count=db_conversation.message_count,
            total_tokens=db_conversation.total_tokens,
            last_activity_at=db_conversation.last_activity_at,
            created_at=db_conversation.created_at,
            updated_at=db_conversation.updated_at,
            messages=messages
        )
