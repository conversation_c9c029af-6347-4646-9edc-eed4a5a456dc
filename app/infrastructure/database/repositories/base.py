"""
Base Repository

This module contains the base repository class with common CRUD operations.
"""

import uuid
from typing import Any, Dict, Generic, List, Optional, Tuple, Type, TypeVar

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.database.base import Base

# Type variable for model classes
ModelType = TypeVar("ModelType", bound=Base)


class BaseRepository(Generic[ModelType]):
    """
    Base repository class providing common CRUD operations.
    
    This class implements the Repository pattern to abstract data access
    and provide a consistent interface for database operations.
    """
    
    def __init__(self, db_session: AsyncSession, model_class: Type[ModelType]):
        """
        Initialize base repository.
        
        Args:
            db_session: Database session
            model_class: SQLAlchemy model class
        """
        self.db_session = db_session
        self.model_class = model_class
    
    async def create(self, entity: ModelType) -> ModelType:
        """
        Create a new entity.
        
        Args:
            entity: Entity to create
            
        Returns:
            ModelType: Created entity
        """
        self.db_session.add(entity)
        await self.db_session.flush()
        await self.db_session.refresh(entity)
        return entity
    
    async def get_by_id(self, entity_id: uuid.UUID) -> Optional[ModelType]:
        """
        Get entity by ID.
        
        Args:
            entity_id: Entity ID
            
        Returns:
            Optional[ModelType]: Entity or None if not found
        """
        result = await self.db_session.execute(
            select(self.model_class).where(self.model_class.id == entity_id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_ids(self, entity_ids: List[uuid.UUID]) -> List[ModelType]:
        """
        Get entities by list of IDs.
        
        Args:
            entity_ids: List of entity IDs
            
        Returns:
            List[ModelType]: List of entities
        """
        result = await self.db_session.execute(
            select(self.model_class).where(self.model_class.id.in_(entity_ids))
        )
        return list(result.scalars().all())
    
    async def update(self, entity: ModelType) -> ModelType:
        """
        Update an existing entity.
        
        Args:
            entity: Entity to update
            
        Returns:
            ModelType: Updated entity
        """
        await self.db_session.merge(entity)
        await self.db_session.flush()
        await self.db_session.refresh(entity)
        return entity
    
    async def delete(self, entity: ModelType) -> None:
        """
        Delete an entity.
        
        Args:
            entity: Entity to delete
        """
        await self.db_session.delete(entity)
        await self.db_session.flush()
    
    async def delete_by_id(self, entity_id: uuid.UUID) -> bool:
        """
        Delete entity by ID.
        
        Args:
            entity_id: Entity ID
            
        Returns:
            bool: True if deleted, False if not found
        """
        entity = await self.get_by_id(entity_id)
        if entity:
            await self.delete(entity)
            return True
        return False
    
    async def list_all(
        self,
        offset: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = None,
        sort_order: str = "asc"
    ) -> Tuple[List[ModelType], int]:
        """
        List all entities with pagination.
        
        Args:
            offset: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)
            
        Returns:
            Tuple[List[ModelType], int]: Entities and total count
        """
        # Build base query
        query = select(self.model_class)
        
        # Add sorting
        if sort_by and hasattr(self.model_class, sort_by):
            sort_column = getattr(self.model_class, sort_by)
            if sort_order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(sort_column)
        else:
            # Default sort by created_at desc
            query = query.order_by(desc(self.model_class.created_at))
        
        # Get total count
        count_query = select(func.count()).select_from(self.model_class)
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination
        query = query.offset(offset).limit(limit)
        
        # Execute query
        result = await self.db_session.execute(query)
        entities = list(result.scalars().all())
        
        return entities, total
    
    async def find_by_field(
        self,
        field_name: str,
        field_value: Any,
        limit: Optional[int] = None
    ) -> List[ModelType]:
        """
        Find entities by a specific field value.
        
        Args:
            field_name: Name of the field to search
            field_value: Value to search for
            limit: Maximum number of results
            
        Returns:
            List[ModelType]: Matching entities
        """
        if not hasattr(self.model_class, field_name):
            raise ValueError(f"Field {field_name} does not exist on {self.model_class.__name__}")
        
        field = getattr(self.model_class, field_name)
        query = select(self.model_class).where(field == field_value)
        
        if limit:
            query = query.limit(limit)
        
        result = await self.db_session.execute(query)
        return list(result.scalars().all())
    
    async def find_one_by_field(
        self,
        field_name: str,
        field_value: Any
    ) -> Optional[ModelType]:
        """
        Find single entity by field value.
        
        Args:
            field_name: Name of the field to search
            field_value: Value to search for
            
        Returns:
            Optional[ModelType]: Matching entity or None
        """
        entities = await self.find_by_field(field_name, field_value, limit=1)
        return entities[0] if entities else None
    
    async def exists(self, entity_id: uuid.UUID) -> bool:
        """
        Check if entity exists by ID.
        
        Args:
            entity_id: Entity ID
            
        Returns:
            bool: True if exists, False otherwise
        """
        result = await self.db_session.execute(
            select(func.count()).where(self.model_class.id == entity_id)
        )
        return result.scalar() > 0
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count entities with optional filters.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            int: Number of entities
        """
        query = select(func.count()).select_from(self.model_class)
        
        if filters:
            conditions = []
            for field_name, field_value in filters.items():
                if hasattr(self.model_class, field_name):
                    field = getattr(self.model_class, field_name)
                    conditions.append(field == field_value)
            
            if conditions:
                query = query.where(and_(*conditions))
        
        result = await self.db_session.execute(query)
        return result.scalar()
    
    async def bulk_create(self, entities: List[ModelType]) -> List[ModelType]:
        """
        Create multiple entities in bulk.
        
        Args:
            entities: List of entities to create
            
        Returns:
            List[ModelType]: Created entities
        """
        self.db_session.add_all(entities)
        await self.db_session.flush()
        
        # Refresh all entities to get generated IDs
        for entity in entities:
            await self.db_session.refresh(entity)
        
        return entities
    
    async def bulk_update(self, updates: List[Dict[str, Any]]) -> None:
        """
        Update multiple entities in bulk.
        
        Args:
            updates: List of update dictionaries with 'id' and update fields
        """
        for update_data in updates:
            entity_id = update_data.pop("id")
            entity = await self.get_by_id(entity_id)
            if entity:
                for field, value in update_data.items():
                    if hasattr(entity, field):
                        setattr(entity, field, value)
        
        await self.db_session.flush()
