"""
Database Session Management

This module handles SQLAlchemy database session creation, configuration,
and lifecycle management.
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import (
    AsyncSession,
    async_sessionmaker,
    create_async_engine
)
from sqlalchemy.pool import NullPool

from app.core.config import get_settings
from app.core.exceptions import DatabaseException
from app.models.database.base import Base

logger = logging.getLogger(__name__)

# Global variables for database engine and session factory
_engine = None
_session_factory = None


def get_database_url() -> str:
    """
    Get database URL from settings.
    
    Returns:
        str: Database connection URL
    """
    settings = get_settings()
    return settings.DATABASE_URL


def create_database_engine():
    """
    Create SQLAlchemy async engine with configuration.
    
    Returns:
        AsyncEngine: Configured database engine
    """
    settings = get_settings()
    
    engine_kwargs = {
        "echo": settings.DEBUG,  # Log SQL queries in debug mode
        "pool_size": settings.DATABASE_POOL_SIZE,
        "max_overflow": settings.DATABASE_MAX_OVERFLOW,
        "pool_timeout": settings.DATABASE_POOL_TIMEOUT,
        "pool_recycle": settings.DATABASE_POOL_RECYCLE,
        "pool_pre_ping": True,  # Validate connections before use
    }
    
    # Use NullPool for testing to avoid connection issues
    if settings.TESTING:
        engine_kwargs["poolclass"] = NullPool
    
    return create_async_engine(
        get_database_url(),
        **engine_kwargs
    )


def get_session_factory():
    """
    Get or create session factory.
    
    Returns:
        async_sessionmaker: Session factory
    """
    global _session_factory
    
    if _session_factory is None:
        engine = get_database_engine()
        _session_factory = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False
        )
    
    return _session_factory


def get_database_engine():
    """
    Get or create database engine.
    
    Returns:
        AsyncEngine: Database engine
    """
    global _engine
    
    if _engine is None:
        _engine = create_database_engine()
    
    return _engine


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get database session context manager.
    
    Yields:
        AsyncSession: Database session
        
    Raises:
        DatabaseException: If session creation fails
    """
    session_factory = get_session_factory()
    
    async with session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise DatabaseException(f"Database operation failed: {str(e)}")
        finally:
            await session.close()


async def init_db() -> None:
    """
    Initialize database by creating all tables.
    
    This function should be called during application startup.
    """
    try:
        engine = get_database_engine()
        
        # Create all tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise DatabaseException(f"Database initialization failed: {str(e)}")


async def close_db() -> None:
    """
    Close database connections.
    
    This function should be called during application shutdown.
    """
    global _engine, _session_factory
    
    try:
        if _engine:
            await _engine.dispose()
            _engine = None
        
        _session_factory = None
        
        logger.info("Database connections closed")
        
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")


async def check_database_health() -> bool:
    """
    Check database connectivity and health.
    
    Returns:
        bool: True if database is healthy, False otherwise
    """
    try:
        async with get_db_session() as session:
            # Execute a simple query to test connectivity
            from sqlalchemy import text
            result = await session.execute(text("SELECT 1"))
            return result.scalar() == 1
            
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


async def create_test_database() -> None:
    """
    Create test database for testing.
    
    This function creates a separate test database to avoid
    interfering with development data.
    """
    settings = get_settings()
    
    if not settings.TESTING:
        raise ValueError("create_test_database should only be called in testing mode")
    
    try:
        # Use test database URL
        test_engine = create_async_engine(settings.TEST_DATABASE_URL)
        
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        await test_engine.dispose()
        logger.info("Test database created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create test database: {e}")
        raise DatabaseException(f"Test database creation failed: {str(e)}")


async def drop_test_database() -> None:
    """
    Drop test database after testing.
    
    This function cleans up the test database after tests complete.
    """
    settings = get_settings()
    
    if not settings.TESTING:
        raise ValueError("drop_test_database should only be called in testing mode")
    
    try:
        # Use test database URL
        test_engine = create_async_engine(settings.TEST_DATABASE_URL)
        
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        await test_engine.dispose()
        logger.info("Test database dropped successfully")
        
    except Exception as e:
        logger.error(f"Failed to drop test database: {e}")
        raise DatabaseException(f"Test database cleanup failed: {str(e)}")
