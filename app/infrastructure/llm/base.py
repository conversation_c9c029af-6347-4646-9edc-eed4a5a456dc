"""
Base LLM Client

This module contains the base LLM client interface and common functionality.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, AsyncGenerator
from dataclasses import dataclass

from app.core.exceptions import LLMException


@dataclass
class LLMMessage:
    """
    Represents a message in LLM conversation.
    """
    role: str  # "system", "user", "assistant", "tool"
    content: str
    name: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None


@dataclass
class LLMResponse:
    """
    Represents an LLM response.
    """
    content: str
    model: str
    usage: Dict[str, int]  # tokens used
    finish_reason: str
    tool_calls: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMStreamChunk:
    """
    Represents a chunk in streaming LLM response.
    """
    content: str
    finish_reason: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    usage: Optional[Dict[str, int]] = None


class BaseLLMClient(ABC):
    """
    Base class for LLM clients.
    
    Defines the interface that all LLM providers must implement.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize LLM client.
        
        Args:
            config: Client configuration
        """
        self.config = config
        self.model = config.get("model", "gpt-3.5-turbo")
        self.temperature = config.get("temperature", 0.7)
        self.max_tokens = config.get("max_tokens", 4096)
        self.timeout = config.get("timeout", 60)
    
    @abstractmethod
    async def generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """
        Generate response from messages.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional generation parameters
            
        Returns:
            LLMResponse: Generated response
            
        Raises:
            LLMException: If generation fails
        """
        pass
    
    @abstractmethod
    async def stream_generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> AsyncGenerator[LLMStreamChunk, None]:
        """
        Generate streaming response from messages.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional generation parameters
            
        Yields:
            LLMStreamChunk: Response chunks
            
        Raises:
            LLMException: If generation fails
        """
        pass
    
    @abstractmethod
    async def get_embedding(
        self,
        text: str,
        **kwargs
    ) -> List[float]:
        """
        Get text embedding.
        
        Args:
            text: Text to embed
            **kwargs: Additional parameters
            
        Returns:
            List[float]: Embedding vector
            
        Raises:
            LLMException: If embedding fails
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """
        Check if LLM service is healthy.
        
        Returns:
            bool: True if healthy, False otherwise
        """
        pass
    
    def format_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """
        Format messages for the specific LLM provider.
        
        Args:
            messages: List of LLM messages
            
        Returns:
            List[Dict[str, Any]]: Formatted messages
        """
        formatted = []
        
        for message in messages:
            formatted_msg = {
                "role": message.role,
                "content": message.content
            }
            
            if message.name:
                formatted_msg["name"] = message.name
            
            if message.tool_calls:
                formatted_msg["tool_calls"] = message.tool_calls
            
            if message.tool_call_id:
                formatted_msg["tool_call_id"] = message.tool_call_id
            
            formatted.append(formatted_msg)
        
        return formatted
    
    def calculate_tokens(self, text: str) -> int:
        """
        Estimate token count for text.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            int: Estimated token count
        """
        # Simple estimation: ~4 characters per token
        return len(text) // 4
    
    def validate_messages(self, messages: List[LLMMessage]) -> None:
        """
        Validate message format and content.
        
        Args:
            messages: Messages to validate
            
        Raises:
            LLMException: If validation fails
        """
        if not messages:
            raise LLMException("Messages list cannot be empty")
        
        valid_roles = {"system", "user", "assistant", "tool"}
        
        for i, message in enumerate(messages):
            if not message.role:
                raise LLMException(f"Message {i} missing role")
            
            if message.role not in valid_roles:
                raise LLMException(f"Message {i} has invalid role: {message.role}")
            
            if not message.content and not message.tool_calls:
                raise LLMException(f"Message {i} missing content")
    
    def get_generation_params(self, **kwargs) -> Dict[str, Any]:
        """
        Get generation parameters with defaults.
        
        Args:
            **kwargs: Override parameters
            
        Returns:
            Dict[str, Any]: Generation parameters
        """
        params = {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "timeout": self.timeout
        }
        
        # Override with provided kwargs
        params.update(kwargs)
        
        return params
