"""
LiteLLM Client

This module contains the LiteLLM client implementation for unified
access to multiple LLM providers.
"""

import logging
from typing import Any, AsyncGenerator, Dict, List, Optional

try:
    import litellm
    from litellm import acompletion, aembedding
except ImportError:
    litellm = None

from app.core.config import get_settings
from app.core.exceptions import LLMException
from app.infrastructure.llm.base import (
    BaseLLMClient,
    LLMMessage,
    LLMResponse,
    LLMStreamChunk
)

logger = logging.getLogger(__name__)


class LiteLLMClient(BaseLLMClient):
    """
    LiteLLM client for unified access to multiple LLM providers.
    
    Supports OpenAI, Anthropic, Azure OpenAI, and other providers
    through the LiteLLM library.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize LiteLLM client.
        
        Args:
            config: Optional client configuration
        """
        if litellm is None:
            raise ImportError("litellm package is required for LiteLLMClient")
        
        # Get configuration from settings if not provided
        if config is None:
            config = self._get_default_config()
        
        super().__init__(config)
        
        # Configure LiteLLM
        self._configure_litellm()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration from settings.
        
        Returns:
            Dict[str, Any]: Default configuration
        """
        settings = get_settings()
        
        return {
            "model": settings.OPENAI_MODEL,
            "temperature": settings.OPENAI_TEMPERATURE,
            "max_tokens": settings.OPENAI_MAX_TOKENS,
            "timeout": 60,
            "provider": settings.DEFAULT_LLM_PROVIDER
        }
    
    def _configure_litellm(self) -> None:
        """Configure LiteLLM with API keys and settings."""
        settings = get_settings()
        
        # Set API keys
        if settings.OPENAI_API_KEY:
            litellm.openai_key = settings.OPENAI_API_KEY
        
        if settings.ANTHROPIC_API_KEY:
            litellm.anthropic_key = settings.ANTHROPIC_API_KEY
        
        if settings.AZURE_OPENAI_API_KEY:
            litellm.azure_key = settings.AZURE_OPENAI_API_KEY
        
        if settings.AZURE_OPENAI_ENDPOINT:
            litellm.azure_base = settings.AZURE_OPENAI_ENDPOINT
        
        # Configure base URL for OpenAI
        if settings.OPENAI_BASE_URL != "https://api.openai.com/v1":
            litellm.openai_base = settings.OPENAI_BASE_URL
        
        # Set logging level
        if settings.DEBUG:
            litellm.set_verbose = True
    
    async def generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """
        Generate response from messages using LiteLLM.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional generation parameters
            
        Returns:
            LLMResponse: Generated response
            
        Raises:
            LLMException: If generation fails
        """
        try:
            # Validate messages
            self.validate_messages(messages)
            
            # Format messages for LiteLLM
            formatted_messages = self.format_messages(messages)
            
            # Get generation parameters
            params = self.get_generation_params(**kwargs)
            
            # Make API call
            response = await acompletion(
                model=params["model"],
                messages=formatted_messages,
                temperature=params["temperature"],
                max_tokens=params["max_tokens"],
                timeout=params["timeout"],
                **{k: v for k, v in kwargs.items() if k not in params}
            )
            
            # Extract response data
            choice = response.choices[0]
            message = choice.message
            
            # Handle tool calls
            tool_calls = None
            if hasattr(message, 'tool_calls') and message.tool_calls:
                tool_calls = [
                    {
                        "id": tc.id,
                        "type": tc.type,
                        "function": {
                            "name": tc.function.name,
                            "arguments": tc.function.arguments
                        }
                    }
                    for tc in message.tool_calls
                ]
            
            return LLMResponse(
                content=message.content or "",
                model=response.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                finish_reason=choice.finish_reason,
                tool_calls=tool_calls,
                metadata={
                    "response_id": response.id,
                    "created": response.created
                }
            )
            
        except Exception as e:
            logger.error(f"LLM generation error: {e}")
            raise LLMException(f"Failed to generate response: {str(e)}")
    
    async def stream_generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> AsyncGenerator[LLMStreamChunk, None]:
        """
        Generate streaming response from messages.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional generation parameters
            
        Yields:
            LLMStreamChunk: Response chunks
            
        Raises:
            LLMException: If generation fails
        """
        try:
            # Validate messages
            self.validate_messages(messages)
            
            # Format messages for LiteLLM
            formatted_messages = self.format_messages(messages)
            
            # Get generation parameters
            params = self.get_generation_params(**kwargs)
            
            # Make streaming API call
            stream = await acompletion(
                model=params["model"],
                messages=formatted_messages,
                temperature=params["temperature"],
                max_tokens=params["max_tokens"],
                timeout=params["timeout"],
                stream=True,
                **{k: v for k, v in kwargs.items() if k not in params}
            )
            
            async for chunk in stream:
                if chunk.choices:
                    choice = chunk.choices[0]
                    delta = choice.delta
                    
                    # Extract content
                    content = getattr(delta, 'content', '') or ''
                    
                    # Extract tool calls
                    tool_calls = None
                    if hasattr(delta, 'tool_calls') and delta.tool_calls:
                        tool_calls = [
                            {
                                "id": tc.id if hasattr(tc, 'id') else None,
                                "type": tc.type if hasattr(tc, 'type') else None,
                                "function": {
                                    "name": tc.function.name if hasattr(tc.function, 'name') else None,
                                    "arguments": tc.function.arguments if hasattr(tc.function, 'arguments') else None
                                } if hasattr(tc, 'function') else None
                            }
                            for tc in delta.tool_calls
                        ]
                    
                    # Extract usage (usually only in last chunk)
                    usage = None
                    if hasattr(chunk, 'usage') and chunk.usage:
                        usage = {
                            "prompt_tokens": chunk.usage.prompt_tokens,
                            "completion_tokens": chunk.usage.completion_tokens,
                            "total_tokens": chunk.usage.total_tokens
                        }
                    
                    yield LLMStreamChunk(
                        content=content,
                        finish_reason=choice.finish_reason,
                        tool_calls=tool_calls,
                        usage=usage
                    )
            
        except Exception as e:
            logger.error(f"LLM streaming error: {e}")
            raise LLMException(f"Failed to stream response: {str(e)}")
    
    async def get_embedding(
        self,
        text: str,
        **kwargs
    ) -> List[float]:
        """
        Get text embedding using LiteLLM.
        
        Args:
            text: Text to embed
            **kwargs: Additional parameters
            
        Returns:
            List[float]: Embedding vector
            
        Raises:
            LLMException: If embedding fails
        """
        try:
            # Use embedding model (default to text-embedding-ada-002)
            model = kwargs.get("model", "text-embedding-ada-002")
            
            response = await aembedding(
                model=model,
                input=text,
                timeout=self.timeout
            )
            
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"LLM embedding error: {e}")
            raise LLMException(f"Failed to get embedding: {str(e)}")
    
    async def health_check(self) -> bool:
        """
        Check if LLM service is healthy.
        
        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            # Simple test with minimal message
            test_messages = [
                LLMMessage(role="user", content="Hello")
            ]
            
            response = await self.generate(
                messages=test_messages,
                max_tokens=1,
                temperature=0
            )
            
            return response.content is not None
            
        except Exception as e:
            logger.error(f"LLM health check failed: {e}")
            return False
    
    def get_supported_models(self) -> List[str]:
        """
        Get list of supported models.
        
        Returns:
            List[str]: Supported model names
        """
        # This is a subset of models supported by LiteLLM
        return [
            # OpenAI
            "gpt-4",
            "gpt-4-turbo-preview",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            
            # Anthropic
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            
            # Azure OpenAI
            "azure/gpt-4",
            "azure/gpt-35-turbo",
            
            # Other providers
            "ollama/llama2",
            "ollama/mistral",
        ]
    
    def estimate_cost(
        self,
        model: str,
        prompt_tokens: int,
        completion_tokens: int
    ) -> float:
        """
        Estimate cost for token usage.
        
        Args:
            model: Model name
            prompt_tokens: Number of prompt tokens
            completion_tokens: Number of completion tokens
            
        Returns:
            float: Estimated cost in USD
        """
        # Simplified cost estimation (prices as of 2024)
        pricing = {
            "gpt-4": {"prompt": 0.03, "completion": 0.06},
            "gpt-4-turbo-preview": {"prompt": 0.01, "completion": 0.03},
            "gpt-3.5-turbo": {"prompt": 0.0015, "completion": 0.002},
            "claude-3-opus-20240229": {"prompt": 0.015, "completion": 0.075},
            "claude-3-sonnet-20240229": {"prompt": 0.003, "completion": 0.015},
            "claude-3-haiku-20240307": {"prompt": 0.00025, "completion": 0.00125},
        }
        
        if model not in pricing:
            return 0.0
        
        model_pricing = pricing[model]
        prompt_cost = (prompt_tokens / 1000) * model_pricing["prompt"]
        completion_cost = (completion_tokens / 1000) * model_pricing["completion"]
        
        return prompt_cost + completion_cost
