"""
Database Tool

This module contains the internal database tool implementation
for querying and manipulating database data.
"""

import logging
from typing import Any, Dict, List, Optional

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import ToolException
from app.infrastructure.database.session import get_db_session

logger = logging.getLogger(__name__)


class DatabaseTool:
    """
    Internal database tool for querying and data operations.
    
    Provides safe database access for agents with query validation
    and result formatting.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize database tool.
        
        Args:
            config: Optional tool configuration
        """
        self.config = config or {}
        self.name = "database_query"
        self.description = "Query database for structured data"
        self.timeout = self.config.get("timeout", 30)
        self.max_rows = self.config.get("max_rows", 100)
        
        # Allowed operations for security
        self.allowed_operations = {
            "SELECT", "SHOW", "DESCRIBE", "EXPLAIN"
        }
        
        logger.info("Initialized database tool")
    
    async def query(
        self,
        sql: str,
        parameters: Optional[Dict[str, Any]] = None,
        max_rows: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Execute a database query.
        
        Args:
            sql: SQL query to execute
            parameters: Optional query parameters
            max_rows: Maximum number of rows to return
            
        Returns:
            Dict[str, Any]: Query results
            
        Raises:
            ToolException: If query fails or is not allowed
        """
        try:
            # Validate query
            self._validate_query(sql)
            
            max_rows = max_rows or self.max_rows
            
            # Execute query
            async with get_db_session() as session:
                result = await self._execute_query(
                    session, sql, parameters, max_rows
                )
            
            return {
                "sql": sql,
                "parameters": parameters,
                "results": result["rows"],
                "columns": result["columns"],
                "row_count": len(result["rows"]),
                "execution_time": result["execution_time"],
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Database tool error: {e}")
            raise ToolException(f"Database query failed: {str(e)}")
    
    def _validate_query(self, sql: str) -> None:
        """
        Validate SQL query for security.
        
        Args:
            sql: SQL query to validate
            
        Raises:
            ToolException: If query is not allowed
        """
        if not sql or not sql.strip():
            raise ToolException("SQL query cannot be empty")
        
        # Convert to uppercase for checking
        sql_upper = sql.strip().upper()
        
        # Check if query starts with allowed operation
        allowed = False
        for operation in self.allowed_operations:
            if sql_upper.startswith(operation):
                allowed = True
                break
        
        if not allowed:
            raise ToolException(
                f"Only {', '.join(self.allowed_operations)} operations are allowed"
            )
        
        # Check for dangerous keywords
        dangerous_keywords = [
            "DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE",
            "TRUNCATE", "GRANT", "REVOKE", "EXEC", "EXECUTE"
        ]
        
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                raise ToolException(f"Keyword '{keyword}' is not allowed")
    
    async def _execute_query(
        self,
        session: AsyncSession,
        sql: str,
        parameters: Optional[Dict[str, Any]],
        max_rows: int
    ) -> Dict[str, Any]:
        """
        Execute the SQL query.
        
        Args:
            session: Database session
            sql: SQL query
            parameters: Query parameters
            max_rows: Maximum rows to return
            
        Returns:
            Dict[str, Any]: Query execution result
        """
        import time
        
        start_time = time.time()
        
        # Prepare query with limit
        limited_sql = self._add_limit_clause(sql, max_rows)
        
        # Execute query
        if parameters:
            result = await session.execute(text(limited_sql), parameters)
        else:
            result = await session.execute(text(limited_sql))
        
        # Fetch results
        rows = result.fetchall()
        columns = list(result.keys()) if rows else []
        
        # Convert rows to dictionaries
        formatted_rows = []
        for row in rows:
            formatted_rows.append(dict(zip(columns, row)))
        
        execution_time = time.time() - start_time
        
        return {
            "rows": formatted_rows,
            "columns": columns,
            "execution_time": execution_time
        }
    
    def _add_limit_clause(self, sql: str, max_rows: int) -> str:
        """
        Add LIMIT clause to SQL query if not present.
        
        Args:
            sql: Original SQL query
            max_rows: Maximum rows to return
            
        Returns:
            str: SQL query with LIMIT clause
        """
        sql_upper = sql.upper()
        
        # Check if LIMIT already exists
        if "LIMIT" in sql_upper:
            return sql
        
        # Add LIMIT clause
        return f"{sql.rstrip(';')} LIMIT {max_rows}"
    
    async def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        Get information about a database table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            Dict[str, Any]: Table information
        """
        try:
            # Validate table name (basic security check)
            if not table_name.replace("_", "").replace("-", "").isalnum():
                raise ToolException("Invalid table name")
            
            # Get table structure
            describe_sql = f"DESCRIBE {table_name}"
            result = await self.query(describe_sql)
            
            return {
                "table_name": table_name,
                "columns": result["results"],
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Get table info error: {e}")
            raise ToolException(f"Failed to get table info: {str(e)}")
    
    async def list_tables(self) -> Dict[str, Any]:
        """
        List all tables in the database.
        
        Returns:
            Dict[str, Any]: List of tables
        """
        try:
            # This query works for most SQL databases
            sql = "SHOW TABLES"
            result = await self.query(sql)
            
            # Extract table names from results
            tables = []
            for row in result["results"]:
                # Table name is usually in the first column
                table_name = list(row.values())[0]
                tables.append(table_name)
            
            return {
                "tables": tables,
                "table_count": len(tables),
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"List tables error: {e}")
            raise ToolException(f"Failed to list tables: {str(e)}")
    
    async def get_sample_data(
        self,
        table_name: str,
        limit: int = 5
    ) -> Dict[str, Any]:
        """
        Get sample data from a table.
        
        Args:
            table_name: Name of the table
            limit: Number of sample rows
            
        Returns:
            Dict[str, Any]: Sample data
        """
        try:
            # Validate table name
            if not table_name.replace("_", "").replace("-", "").isalnum():
                raise ToolException("Invalid table name")
            
            # Get sample data
            sql = f"SELECT * FROM {table_name}"
            result = await self.query(sql, max_rows=limit)
            
            return {
                "table_name": table_name,
                "sample_data": result["results"],
                "columns": result["columns"],
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Get sample data error: {e}")
            raise ToolException(f"Failed to get sample data: {str(e)}")
    
    def get_tool_info(self) -> Dict[str, Any]:
        """
        Get tool information and capabilities.
        
        Returns:
            Dict[str, Any]: Tool information
        """
        return {
            "name": self.name,
            "description": self.description,
            "capabilities": [
                "sql_query",
                "table_info",
                "list_tables",
                "sample_data"
            ],
            "allowed_operations": list(self.allowed_operations),
            "parameters": {
                "query": {
                    "sql": {"type": "string", "required": True},
                    "parameters": {"type": "object", "required": False},
                    "max_rows": {"type": "integer", "default": 100}
                },
                "get_table_info": {
                    "table_name": {"type": "string", "required": True}
                },
                "get_sample_data": {
                    "table_name": {"type": "string", "required": True},
                    "limit": {"type": "integer", "default": 5}
                }
            },
            "config": {
                "timeout": self.timeout,
                "max_rows": self.max_rows
            },
            "security": {
                "read_only": True,
                "allowed_operations": list(self.allowed_operations)
            }
        }
