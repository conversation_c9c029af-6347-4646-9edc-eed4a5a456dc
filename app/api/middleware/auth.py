"""
Authentication Middleware

This module contains middleware for handling authentication and authorization.
"""

import logging
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class AuthMiddleware(BaseHTTPMiddleware):
    """
    Authentication middleware for processing authentication headers
    and setting user context.
    """
    
    def __init__(self, app, skip_paths: list[str] = None):
        """
        Initialize authentication middleware.
        
        Args:
            app: FastAPI application
            skip_paths: List of paths to skip authentication
        """
        super().__init__(app)
        self.skip_paths = skip_paths or [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/health",
            "/api/v1/health/live",
            "/api/v1/health/ready"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process authentication for incoming requests.
        
        Args:
            request: Incoming HTTP request
            call_next: Next middleware/endpoint in chain
            
        Returns:
            Response: HTTP response
        """
        # Skip authentication for certain paths
        if any(request.url.path.startswith(path) for path in self.skip_paths):
            return await call_next(request)
        
        # Extract authentication information
        auth_header = request.headers.get("Authorization")
        api_key = request.headers.get("X-API-Key")
        
        # Set authentication context in request state
        request.state.auth_header = auth_header
        request.state.api_key = api_key
        request.state.user = None
        
        # TODO: Implement authentication logic
        # 1. Validate JWT token or API key
        # 2. Load user information
        # 3. Set user context in request state
        # 4. Handle authentication errors
        
        try:
            # Process request
            response = await call_next(request)
            
            # Add authentication-related headers to response
            if hasattr(request.state, "user") and request.state.user:
                response.headers["X-User-ID"] = str(request.state.user.get("id", ""))
            
            return response
            
        except Exception as e:
            logger.error(f"Authentication middleware error: {e}")
            # Let the error propagate to be handled by exception handlers
            raise
