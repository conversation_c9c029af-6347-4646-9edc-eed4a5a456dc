"""
Rate Limiting Middleware

This module contains middleware for API rate limiting and throttling.
"""

import json
import logging
import time
from typing import Callable, Optional

from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import get_settings
from app.core.constants import RATE_LIMIT_HEADERS
from app.infrastructure.cache.redis_client import get_redis_client

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware using Redis for distributed rate limiting.
    """
    
    def __init__(
        self,
        app,
        requests_per_minute: int = None,
        burst_size: int = None,
        skip_paths: list[str] = None
    ):
        """
        Initialize rate limiting middleware.
        
        Args:
            app: FastAPI application
            requests_per_minute: Requests allowed per minute
            burst_size: Burst size for token bucket
            skip_paths: List of paths to skip rate limiting
        """
        super().__init__(app)
        settings = get_settings()
        
        self.requests_per_minute = requests_per_minute or settings.RATE_LIMIT_REQUESTS_PER_MINUTE
        self.burst_size = burst_size or settings.RATE_LIMIT_BURST
        self.skip_paths = skip_paths or [
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/api/v1/health/live",
            "/api/v1/health/ready"
        ]
        
        # Calculate rate limiting parameters
        self.window_size = 60  # 1 minute window
        self.tokens_per_second = self.requests_per_minute / 60.0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Apply rate limiting to incoming requests.
        
        Args:
            request: Incoming HTTP request
            call_next: Next middleware/endpoint in chain
            
        Returns:
            Response: HTTP response
            
        Raises:
            HTTPException: If rate limit is exceeded
        """
        # Skip rate limiting for certain paths
        if any(request.url.path.startswith(path) for path in self.skip_paths):
            return await call_next(request)
        
        # Get rate limiting key
        rate_limit_key = await self._get_rate_limit_key(request)
        
        if rate_limit_key:
            # Check rate limit
            allowed, remaining, reset_time = await self._check_rate_limit(rate_limit_key)
            
            if not allowed:
                # Rate limit exceeded
                await self._log_rate_limit_exceeded(request, rate_limit_key)
                
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Rate limit exceeded",
                    headers={
                        RATE_LIMIT_HEADERS["X-RateLimit-Limit"]: str(self.requests_per_minute),
                        RATE_LIMIT_HEADERS["X-RateLimit-Remaining"]: str(remaining),
                        RATE_LIMIT_HEADERS["X-RateLimit-Reset"]: str(reset_time),
                        "Retry-After": str(int(reset_time - time.time()))
                    }
                )
            
            # Process request
            response = await call_next(request)
            
            # Add rate limit headers to response
            response.headers[RATE_LIMIT_HEADERS["X-RateLimit-Limit"]] = str(self.requests_per_minute)
            response.headers[RATE_LIMIT_HEADERS["X-RateLimit-Remaining"]] = str(remaining)
            response.headers[RATE_LIMIT_HEADERS["X-RateLimit-Reset"]] = str(reset_time)
            
            return response
        
        else:
            # No rate limiting key, proceed without rate limiting
            return await call_next(request)
    
    async def _get_rate_limit_key(self, request: Request) -> Optional[str]:
        """
        Generate rate limiting key for the request.
        
        Args:
            request: HTTP request
            
        Returns:
            Optional[str]: Rate limiting key or None to skip rate limiting
        """
        # Try to get user ID from authentication
        user_id = None
        if hasattr(request.state, "user") and request.state.user:
            user_id = request.state.user.get("id")
        
        if user_id:
            # Use user-based rate limiting
            return f"rate_limit:user:{user_id}:{request.url.path}"
        
        # Fall back to IP-based rate limiting
        client_ip = self._get_client_ip(request)
        if client_ip and client_ip != "unknown":
            return f"rate_limit:ip:{client_ip}:{request.url.path}"
        
        # No valid key for rate limiting
        return None
    
    async def _check_rate_limit(self, key: str) -> tuple[bool, int, float]:
        """
        Check if request is within rate limit using token bucket algorithm.
        
        Args:
            key: Rate limiting key
            
        Returns:
            tuple: (allowed, remaining_tokens, reset_time)
        """
        try:
            redis = await get_redis_client()
            current_time = time.time()
            
            # Use Redis pipeline for atomic operations
            pipe = redis.pipeline()
            
            # Get current bucket state
            bucket_key = f"{key}:bucket"
            last_refill_key = f"{key}:last_refill"
            
            # Get current values
            bucket_data = await redis.hmget(bucket_key, "tokens", "last_refill")
            current_tokens = float(bucket_data[0] or self.burst_size)
            last_refill = float(bucket_data[1] or current_time)
            
            # Calculate tokens to add based on time elapsed
            time_elapsed = current_time - last_refill
            tokens_to_add = time_elapsed * self.tokens_per_second
            
            # Update token count (capped at burst size)
            new_tokens = min(self.burst_size, current_tokens + tokens_to_add)
            
            # Check if request can be allowed
            if new_tokens >= 1.0:
                # Allow request and consume token
                new_tokens -= 1.0
                allowed = True
            else:
                # Deny request
                allowed = False
            
            # Update bucket state
            await redis.hmset(bucket_key, {
                "tokens": new_tokens,
                "last_refill": current_time
            })
            
            # Set expiration for cleanup
            await redis.expire(bucket_key, self.window_size * 2)
            
            # Calculate reset time (when bucket will be full again)
            tokens_needed = self.burst_size - new_tokens
            reset_time = current_time + (tokens_needed / self.tokens_per_second)
            
            return allowed, int(new_tokens), reset_time
            
        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # On error, allow the request (fail open)
            return True, self.requests_per_minute, time.time() + self.window_size
    
    async def _log_rate_limit_exceeded(self, request: Request, key: str) -> None:
        """
        Log rate limit exceeded event.
        
        Args:
            request: HTTP request
            key: Rate limiting key
        """
        log_data = {
            "event": "rate_limit_exceeded",
            "rate_limit_key": key,
            "method": request.method,
            "path": request.url.path,
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("User-Agent", ""),
            "trace_id": getattr(request.state, "trace_id", "")
        }
        
        logger.warning(json.dumps(log_data))
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Extract client IP address from request.
        
        Args:
            request: HTTP request
            
        Returns:
            str: Client IP address
        """
        # Check for forwarded headers (load balancer, proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if request.client:
            return request.client.host
        
        return "unknown"
