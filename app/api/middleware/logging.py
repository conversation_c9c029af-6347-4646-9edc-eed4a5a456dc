"""
Logging Middleware

This module contains middleware for request/response logging and tracing.
"""

import json
import logging
import time
import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Logging middleware for request/response logging and performance monitoring.
    """
    
    def __init__(self, app, log_requests: bool = True, log_responses: bool = True):
        """
        Initialize logging middleware.
        
        Args:
            app: FastAPI application
            log_requests: Whether to log incoming requests
            log_responses: Whether to log outgoing responses
        """
        super().__init__(app)
        self.log_requests = log_requests
        self.log_responses = log_responses
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process logging for incoming requests and outgoing responses.
        
        Args:
            request: Incoming HTTP request
            call_next: Next middleware/endpoint in chain
            
        Returns:
            Response: HTTP response
        """
        # Generate or extract trace ID
        trace_id = request.headers.get("X-Trace-ID") or str(uuid.uuid4())
        request.state.trace_id = trace_id
        
        # Record start time
        start_time = time.time()
        
        # Log incoming request
        if self.log_requests:
            await self._log_request(request, trace_id)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Add trace ID and timing headers
            response.headers["X-Trace-ID"] = trace_id
            response.headers["X-Process-Time"] = str(process_time)
            
            # Log outgoing response
            if self.log_responses:
                await self._log_response(request, response, process_time, trace_id)
            
            return response
            
        except Exception as e:
            # Log error
            process_time = time.time() - start_time
            await self._log_error(request, e, process_time, trace_id)
            raise
    
    async def _log_request(self, request: Request, trace_id: str) -> None:
        """
        Log incoming request details.
        
        Args:
            request: HTTP request
            trace_id: Request trace ID
        """
        # Extract request information
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "")
        
        # Create log entry
        log_data = {
            "event": "request_received",
            "trace_id": trace_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "client_ip": client_ip,
            "user_agent": user_agent,
            "headers": dict(request.headers),
        }
        
        # Remove sensitive headers
        sensitive_headers = ["authorization", "x-api-key", "cookie"]
        for header in sensitive_headers:
            if header in log_data["headers"]:
                log_data["headers"][header] = "[REDACTED]"
        
        logger.info(json.dumps(log_data))
    
    async def _log_response(
        self,
        request: Request,
        response: Response,
        process_time: float,
        trace_id: str
    ) -> None:
        """
        Log outgoing response details.
        
        Args:
            request: HTTP request
            response: HTTP response
            process_time: Request processing time
            trace_id: Request trace ID
        """
        log_data = {
            "event": "response_sent",
            "trace_id": trace_id,
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "process_time": process_time,
            "response_headers": dict(response.headers),
        }
        
        # Log at appropriate level based on status code
        if response.status_code >= 500:
            logger.error(json.dumps(log_data))
        elif response.status_code >= 400:
            logger.warning(json.dumps(log_data))
        else:
            logger.info(json.dumps(log_data))
    
    async def _log_error(
        self,
        request: Request,
        error: Exception,
        process_time: float,
        trace_id: str
    ) -> None:
        """
        Log request processing error.
        
        Args:
            request: HTTP request
            error: Exception that occurred
            process_time: Request processing time
            trace_id: Request trace ID
        """
        log_data = {
            "event": "request_error",
            "trace_id": trace_id,
            "method": request.method,
            "path": request.url.path,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "process_time": process_time,
        }
        
        logger.error(json.dumps(log_data), exc_info=True)
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Extract client IP address from request.
        
        Args:
            request: HTTP request
            
        Returns:
            str: Client IP address
        """
        # Check for forwarded headers (load balancer, proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if request.client:
            return request.client.host
        
        return "unknown"
