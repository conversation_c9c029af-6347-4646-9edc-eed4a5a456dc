"""
API Dependencies

This module contains dependency injection functions for FastAPI endpoints.
"""

from typing import Optional

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer

from app.core.exceptions import AuthenticationException, AuthorizationException
from app.core.security import verify_token, verify_api_key
from app.infrastructure.database.session import get_db_session
from app.infrastructure.cache.redis_client import get_redis_client
from app.models.schemas.request import PaginationRequest
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis

# Security scheme for JWT tokens
security = HTTPBearer(auto_error=False)


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> dict:
    """
    Get current authenticated user from JWT token.
    
    Args:
        request: FastAPI request object
        credentials: HTTP authorization credentials
        
    Returns:
        dict: User information from token
        
    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        payload = verify_token(credentials.credentials, "access")
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token: missing user ID",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # TODO: Load user from database and validate
        user = {
            "id": user_id,
            "scopes": payload.get("scopes", []),
            "username": payload.get("username"),
            "email": payload.get("email")
        }
        
        return user
        
    except AuthenticationException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user_optional(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[dict]:
    """
    Get current user if authenticated, otherwise return None.
    
    Args:
        request: FastAPI request object
        credentials: HTTP authorization credentials
        
    Returns:
        Optional[dict]: User information or None
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(request, credentials)
    except HTTPException:
        return None


async def verify_api_key_dependency(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> dict:
    """
    Verify API key authentication.
    
    Args:
        request: FastAPI request object
        credentials: HTTP authorization credentials
        
    Returns:
        dict: API key information
        
    Raises:
        HTTPException: If API key is invalid
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        payload = verify_api_key(credentials.credentials)
        return payload
        
    except AuthenticationException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


def require_permissions(required_scopes: list[str]):
    """
    Dependency factory for requiring specific permissions.
    
    Args:
        required_scopes: List of required permission scopes
        
    Returns:
        Dependency function
    """
    async def permission_dependency(
        current_user: dict = Depends(get_current_user)
    ) -> dict:
        """Check if user has required permissions."""
        user_scopes = current_user.get("scopes", [])
        
        missing_scopes = set(required_scopes) - set(user_scopes)
        if missing_scopes:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing_scopes)}"
            )
        
        return current_user
    
    return permission_dependency


async def get_pagination(
    page: int = 1,
    page_size: int = 20,
    sort_by: Optional[str] = None,
    sort_order: str = "asc"
) -> PaginationRequest:
    """
    Get pagination parameters.
    
    Args:
        page: Page number
        page_size: Items per page
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        
    Returns:
        PaginationRequest: Pagination parameters
    """
    return PaginationRequest(
        page=page,
        page_size=page_size,
        sort_by=sort_by,
        sort_order=sort_order
    )


async def get_database() -> AsyncSession:
    """
    Get database session dependency.
    
    Returns:
        AsyncSession: Database session
    """
    async with get_db_session() as session:
        yield session


async def get_cache() -> Redis:
    """
    Get Redis cache dependency.
    
    Returns:
        Redis: Redis client
    """
    return await get_redis_client()


def get_trace_id(request: Request) -> str:
    """
    Get or generate trace ID for request tracking.
    
    Args:
        request: FastAPI request object
        
    Returns:
        str: Trace ID
    """
    # Try to get trace ID from headers
    trace_id = request.headers.get("X-Trace-ID")
    
    if not trace_id:
        # Generate new trace ID
        import uuid
        trace_id = str(uuid.uuid4())
    
    return trace_id


# Common dependencies for admin operations
require_admin = require_permissions(["admin"])

# Common dependencies for agent operations
require_agent_access = require_permissions(["agent:read"])
require_agent_write = require_permissions(["agent:write"])

# Common dependencies for conversation operations
require_conversation_access = require_permissions(["conversation:read"])
require_conversation_write = require_permissions(["conversation:write"])
