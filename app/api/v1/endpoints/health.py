"""
Health Check Endpoints

This module contains health check and status endpoints for monitoring
and service discovery.
"""

from datetime import datetime
from typing import Dict

from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse

from app.core.config import get_settings
from app.infrastructure.database.session import check_database_health
from app.infrastructure.cache.redis_client import check_redis_health
from app.models.schemas.response import APIResponse, HealthCheckResponse

router = APIRouter()


@router.get(
    "/health",
    response_model=APIResponse[HealthCheckResponse],
    status_code=status.HTTP_200_OK,
    summary="Basic health check",
    description="Returns basic application health status"
)
async def health_check() -> APIResponse[HealthCheckResponse]:
    """
    Basic health check endpoint.
    
    Returns:
        APIResponse[HealthCheckResponse]: Health status
    """
    settings = get_settings()
    
    health_data = HealthCheckResponse(
        status="healthy",
        version=settings.APP_VERSION,
        timestamp=datetime.utcnow(),
        services={}
    )
    
    return APIResponse.success_response(
        data=health_data,
        message="Service is healthy"
    )


