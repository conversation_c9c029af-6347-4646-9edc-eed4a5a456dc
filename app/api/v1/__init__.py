"""
API v1 Package

This package contains version 1 of the API endpoints.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import agents, conversations, health
from app.core.constants import API_V1_PREFIX

# Create the main API router
api_router = APIRouter(prefix=API_V1_PREFIX)

# Include endpoint routers
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)

api_router.include_router(
    conversations.router,
    prefix="/conversations",
    tags=["conversations"]
)

api_router.include_router(
    agents.router,
    prefix="/agents", 
    tags=["agents"]
)
