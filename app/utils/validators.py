"""
Validation Utilities

This module contains validation functions and utilities
for data validation throughout the application.
"""

import re
import uuid
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from app.core.exceptions import ValidationException


def validate_email(email: str) -> bool:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not email or not isinstance(email, str):
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_uuid(uuid_string: str) -> bool:
    """
    Validate UUID format.
    
    Args:
        uuid_string: UUID string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        uuid.UUID(uuid_string)
        return True
    except (ValueError, TypeError):
        return False


def validate_url(url: str) -> bool:
    """
    Validate URL format.
    
    Args:
        url: URL to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_password(password: str) -> Dict[str, Any]:
    """
    Validate password strength.
    
    Args:
        password: Password to validate
        
    Returns:
        Dict[str, Any]: Validation result with details
    """
    result = {
        "valid": True,
        "errors": [],
        "strength": "weak"
    }
    
    if not password or not isinstance(password, str):
        result["valid"] = False
        result["errors"].append("Password is required")
        return result
    
    # Check minimum length
    if len(password) < 8:
        result["valid"] = False
        result["errors"].append("Password must be at least 8 characters long")
    
    # Check maximum length
    if len(password) > 128:
        result["valid"] = False
        result["errors"].append("Password must be less than 128 characters")
    
    # Check for uppercase letter
    if not re.search(r'[A-Z]', password):
        result["valid"] = False
        result["errors"].append("Password must contain at least one uppercase letter")
    
    # Check for lowercase letter
    if not re.search(r'[a-z]', password):
        result["valid"] = False
        result["errors"].append("Password must contain at least one lowercase letter")
    
    # Check for digit
    if not re.search(r'\d', password):
        result["valid"] = False
        result["errors"].append("Password must contain at least one digit")
    
    # Check for special character
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        result["valid"] = False
        result["errors"].append("Password must contain at least one special character")
    
    # Determine strength
    if result["valid"]:
        score = 0
        if len(password) >= 12:
            score += 1
        if re.search(r'[A-Z]', password):
            score += 1
        if re.search(r'[a-z]', password):
            score += 1
        if re.search(r'\d', password):
            score += 1
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 1
        
        if score >= 4:
            result["strength"] = "strong"
        elif score >= 3:
            result["strength"] = "medium"
    
    return result


def validate_json_schema(data: Any, schema: Dict[str, Any]) -> None:
    """
    Validate data against a simple JSON schema.
    
    Args:
        data: Data to validate
        schema: Schema definition
        
    Raises:
        ValidationException: If validation fails
    """
    def validate_field(value: Any, field_schema: Dict[str, Any], field_name: str = ""):
        field_type = field_schema.get("type")
        required = field_schema.get("required", False)
        
        # Check if field is required
        if required and value is None:
            raise ValidationException(f"Field '{field_name}' is required")
        
        if value is None:
            return
        
        # Type validation
        if field_type == "string" and not isinstance(value, str):
            raise ValidationException(f"Field '{field_name}' must be a string")
        elif field_type == "integer" and not isinstance(value, int):
            raise ValidationException(f"Field '{field_name}' must be an integer")
        elif field_type == "number" and not isinstance(value, (int, float)):
            raise ValidationException(f"Field '{field_name}' must be a number")
        elif field_type == "boolean" and not isinstance(value, bool):
            raise ValidationException(f"Field '{field_name}' must be a boolean")
        elif field_type == "array" and not isinstance(value, list):
            raise ValidationException(f"Field '{field_name}' must be an array")
        elif field_type == "object" and not isinstance(value, dict):
            raise ValidationException(f"Field '{field_name}' must be an object")
        
        # Additional validations
        if field_type == "string":
            min_length = field_schema.get("min_length")
            max_length = field_schema.get("max_length")
            
            if min_length and len(value) < min_length:
                raise ValidationException(
                    f"Field '{field_name}' must be at least {min_length} characters"
                )
            
            if max_length and len(value) > max_length:
                raise ValidationException(
                    f"Field '{field_name}' must be at most {max_length} characters"
                )
        
        elif field_type in ["integer", "number"]:
            minimum = field_schema.get("minimum")
            maximum = field_schema.get("maximum")
            
            if minimum is not None and value < minimum:
                raise ValidationException(
                    f"Field '{field_name}' must be at least {minimum}"
                )
            
            if maximum is not None and value > maximum:
                raise ValidationException(
                    f"Field '{field_name}' must be at most {maximum}"
                )
    
    # Validate root object
    if not isinstance(data, dict):
        raise ValidationException("Data must be an object")
    
    # Validate each field
    for field_name, field_schema in schema.get("properties", {}).items():
        value = data.get(field_name)
        validate_field(value, field_schema, field_name)


def validate_file_type(filename: str, allowed_types: List[str]) -> bool:
    """
    Validate file type by extension.
    
    Args:
        filename: Name of the file
        allowed_types: List of allowed file extensions
        
    Returns:
        bool: True if file type is allowed
    """
    if not filename or not isinstance(filename, str):
        return False
    
    # Get file extension
    extension = filename.lower().split('.')[-1] if '.' in filename else ''
    
    return extension in [ext.lower() for ext in allowed_types]


def validate_file_size(file_size: int, max_size: int) -> bool:
    """
    Validate file size.
    
    Args:
        file_size: Size of the file in bytes
        max_size: Maximum allowed size in bytes
        
    Returns:
        bool: True if file size is within limit
    """
    return isinstance(file_size, int) and 0 < file_size <= max_size


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe storage.
    
    Args:
        filename: Original filename
        
    Returns:
        str: Sanitized filename
    """
    if not filename:
        return "unnamed_file"
    
    # Remove or replace dangerous characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip('. ')
    
    # Limit length
    if len(sanitized) > 255:
        name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
        max_name_length = 255 - len(ext) - 1 if ext else 255
        sanitized = name[:max_name_length] + ('.' + ext if ext else '')
    
    return sanitized or "unnamed_file"


def validate_pagination(page: int, page_size: int, max_page_size: int = 100) -> None:
    """
    Validate pagination parameters.
    
    Args:
        page: Page number (1-based)
        page_size: Number of items per page
        max_page_size: Maximum allowed page size
        
    Raises:
        ValidationException: If parameters are invalid
    """
    if not isinstance(page, int) or page < 1:
        raise ValidationException("Page must be a positive integer")
    
    if not isinstance(page_size, int) or page_size < 1:
        raise ValidationException("Page size must be a positive integer")
    
    if page_size > max_page_size:
        raise ValidationException(f"Page size cannot exceed {max_page_size}")


def validate_sort_params(sort_by: str, sort_order: str, allowed_fields: List[str]) -> None:
    """
    Validate sorting parameters.
    
    Args:
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        allowed_fields: List of allowed sort fields
        
    Raises:
        ValidationException: If parameters are invalid
    """
    if sort_by and sort_by not in allowed_fields:
        raise ValidationException(
            f"Invalid sort field. Allowed fields: {', '.join(allowed_fields)}"
        )
    
    if sort_order and sort_order.lower() not in ["asc", "desc"]:
        raise ValidationException("Sort order must be 'asc' or 'desc'")


def validate_date_range(start_date: str, end_date: str) -> None:
    """
    Validate date range.
    
    Args:
        start_date: Start date in ISO format
        end_date: End date in ISO format
        
    Raises:
        ValidationException: If date range is invalid
    """
    from datetime import datetime
    
    try:
        start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        if start >= end:
            raise ValidationException("Start date must be before end date")
            
    except ValueError as e:
        raise ValidationException(f"Invalid date format: {str(e)}")


def validate_agent_config(config: Dict[str, Any]) -> None:
    """
    Validate agent configuration.
    
    Args:
        config: Agent configuration to validate
        
    Raises:
        ValidationException: If configuration is invalid
    """
    required_fields = ["name", "version"]
    
    for field in required_fields:
        if field not in config:
            raise ValidationException(f"Agent config missing required field: {field}")
    
    # Validate specific fields
    if not isinstance(config["name"], str) or not config["name"].strip():
        raise ValidationException("Agent name must be a non-empty string")
    
    if not isinstance(config["version"], str) or not config["version"].strip():
        raise ValidationException("Agent version must be a non-empty string")
    
    # Validate optional numeric fields
    numeric_fields = ["max_iterations", "timeout", "temperature"]
    for field in numeric_fields:
        if field in config:
            value = config[field]
            if not isinstance(value, (int, float)) or value < 0:
                raise ValidationException(f"Agent config field '{field}' must be a non-negative number")
