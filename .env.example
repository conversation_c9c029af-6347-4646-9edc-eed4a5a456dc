# Application Configuration
APP_NAME=super-v5
APP_VERSION=0.1.0
APP_DESCRIPTION="Super-V5 Enterprise LLM Application Backend Service Platform"
DEBUG=true
ENVIRONMENT=development

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=1
RELOAD=true
ALLOWED_HOSTS=["*"]

# Database Configuration
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/super_v5
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production-make-it-at-least-32-characters-long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# LLM Configuration
DEFAULT_LLM_PROVIDER=openai
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.7

# Alternative LLM Providers
ANTHROPIC_API_KEY=your-anthropic-api-key
AZURE_OPENAI_API_KEY=your-azure-openai-api-key
AZURE_OPENAI_ENDPOINT=your-azure-openai-endpoint

# LangGraph Configuration
LANGGRAPH_CHECKPOINT_BACKEND=redis
LANGGRAPH_MAX_ITERATIONS=50
LANGGRAPH_TIMEOUT_SECONDS=300

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SASL_MECHANISM=PLAIN
KAFKA_SASL_USERNAME=
KAFKA_SASL_PASSWORD=

# Apollo Configuration Center
APOLLO_APP_ID=super-v5
APOLLO_CLUSTER=default
APOLLO_NAMESPACE=application
APOLLO_META_SERVER=http://localhost:8080

# Monitoring & Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_METRICS=true
METRICS_PORT=9090
SENTRY_DSN=

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# File Upload
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=["txt", "pdf", "docx", "md"]

# Agent Configuration
DEFAULT_AGENT_TYPE=v5_agent
AGENT_TIMEOUT_SECONDS=300
MAX_CONCURRENT_AGENTS=10

# Tool Configuration
ENABLE_EXTERNAL_TOOLS=true
TOOL_TIMEOUT_SECONDS=30
MAX_TOOL_RETRIES=3

# Development & Testing
TESTING=false
TEST_DATABASE_URL=postgresql+asyncpg://test:test@localhost:5432/super_v5_test
PYTEST_TIMEOUT=30
