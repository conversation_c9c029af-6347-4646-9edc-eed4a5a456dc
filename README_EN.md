# Super-V5 Enterprise LLM Application Backend Service Platform

<div align="center">
  
[![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115.12+-green.svg)](https://fastapi.tiangolo.com/)
[![LangGraph](https://img.shields.io/badge/LangGraph-0.6.0+-purple.svg)](https://python.langchain.com/docs/langgraph)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

A production-grade backend service platform for Large Language Model applications, built with FastAPI, LangGraph, and modern Python technologies using Domain-Driven Design (DDD) and Clean Architecture principles.

[🚀 Quick Start](#quick-start) • [📖 Documentation](#documentation) • [🛠️ Deployment](#deployment) • [🤝 Contributing](#contributing)

</div>

## ✨ Key Features

### 🎯 Core Capabilities
- **Multi-Agent Architecture** - Support for various agent types (V5 Agent, Deep Research, Task Planner)
- **Workflow Orchestration** - LangGraph-based state machine workflow management
- **Tool Ecosystem** - Rich built-in tools and extensible tool registry
- **Real-time Communication** - Streaming responses and conversation state management

### 🏗️ Architecture Design
- **Layered Architecture** - Clear separation of API, Service, Domain, and Infrastructure layers
- **Event-Driven** - Asynchronous processing and event-driven agent execution
- **Microservice Ready** - Modular design supporting independent deployment and scaling
- **Cloud Native** - Containerized deployment with Kubernetes orchestration support

### 🔧 Technology Stack
- **Backend Framework**: FastAPI 0.115.12
- **Agent Framework**: LangGraph 0.6.0
- **Data Validation**: Pydantic 2.11.7
- **Database**: PostgreSQL 16+ / Redis 7.0+
- **Monitoring**: Prometheus + Grafana
- **Deployment**: Docker + Kubernetes

### 🛡️ Production Features
- **High Availability** - Master-slave replication, load balancing, failover
- **Security** - JWT authentication, input validation, rate limiting
- **Monitoring** - Full-stack monitoring, metrics collection, alerting
- **Performance** - Multi-level caching, connection pooling, concurrency optimization

## 🚀 Quick Start

### Prerequisites

- Python 3.12+ (managed by uv)
- [uv](https://docs.astral.sh/uv/) - Fast Python package manager
- Docker & Docker Compose (optional)
- PostgreSQL 16+
- Redis 7.0+

### Install uv

```bash
# Linux/macOS
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Verify installation
uv --version
```

### Local Development

1. **Clone the Repository**
```bash
git clone https://github.com/super-v5/super-v5.git
cd super-v5
```

2. **Automated Setup (Recommended)**
```bash
# Run automated setup script
make setup

# Or run script directly
./scripts/setup.sh        # Linux/macOS
scripts\setup.bat          # Windows
```

3. **Manual Setup**
```bash
# Create virtual environment with Python 3.12
make venv-create

# Install dependencies
make dev-install

# Activate virtual environment
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate     # Windows
```

4. **Configure Environment**
```bash
# Copy environment configuration
cp .env.example .env

# Edit configuration file
vim .env
```

5. **Initialize Database**
```bash
# Initialize database and seed data
make init-db
```

6. **Start Development Server**
```bash
# Start the application
make run

# Or manually
uvicorn app.main:app --reload
```

7. **Verify Installation**
```bash
# Access API documentation
open http://localhost:8000/docs

# Health check
curl http://localhost:8000/api/v1/health
```

### Quick Commands

```bash
# Virtual environment management
make venv-create    # Create venv with Python 3.12
make venv-info      # Show venv information
make venv-remove    # Remove virtual environment

# Development
make dev-install    # Install dev dependencies
make test          # Run tests
make lint          # Run linting
make format        # Format code
make run           # Start dev server

# Database
make migrate       # Run migrations
make seed          # Seed initial data
make init-db       # Initialize database
```

### Docker Deployment

```bash
# Build image
docker build -t super-v5:latest .

# Start complete service stack
docker-compose up -d

# Check service status
docker-compose ps
```

## 📚 API Usage Examples

### Execute Agent

```python
import httpx
import asyncio

async def execute_v5_agent():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/conversations/chat",
            json={
                "message": "Hello, how can you help me today?",
                "agent_type": "v5_agent",
                "agent_config": {
                    "temperature": 0.7,
                    "max_tokens": 1000
                }
            },
            headers={"Authorization": "Bearer your_token_here"}
        )
        
        result = response.json()
        print(f"Agent Response: {result['data']['assistant_message']['content']}")

# Run example
asyncio.run(execute_v5_agent())
```

### Streaming Response

```python
async def stream_agent_response():
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            "http://localhost:8000/api/v1/conversations/chat",
            json={
                "message": "Tell me a story",
                "stream": True
            }
        ) as response:
            async for chunk in response.aiter_text():
                if chunk.startswith("data: "):
                    data = json.loads(chunk[6:])
                    print(f"Streaming data: {data}")
```

### Get Agent List

```bash
curl -X GET "http://localhost:8000/api/v1/agents" \
  -H "Authorization: Bearer your_token_here"
```

## 🏗️ Project Structure

```
super-v5/
├── app/                           # Main application directory
│   ├── api/                       # API layer
│   │   ├── v1/endpoints/         # API endpoints
│   │   └── middleware/           # Middleware
│   ├── agents/                   # Agent business layer
│   │   ├── base/                # Agent base framework
│   │   └── business/            # Specific agent implementations
│   ├── services/                # Business service layer
│   ├── models/                  # Data models
│   │   ├── database/            # Database models
│   │   ├── schemas/             # Pydantic schemas
│   │   └── domain/              # Domain entities
│   ├── infrastructure/          # Infrastructure layer
│   │   ├── database/            # Database access
│   │   ├── cache/               # Caching
│   │   └── llm/                 # LLM integrations
│   ├── tools/                   # Tool implementations
│   └── utils/                   # Utility functions
├── tests/                       # Test directory
├── scripts/                     # Script directory
├── migrations/                  # Database migrations
└── docs/                       # Documentation
```

## 🤖 Agent Types

### V5 Agent (v5_agent)
General-purpose conversational AI agent
```python
# Usage example
{
    "agent_type": "v5_agent",
    "message": "Help me plan my day",
    "agent_config": {"temperature": 0.7}
}
```

### Deep Research Agent (deep_research)
Specialized for research and analysis tasks
```python
# Usage example
{
    "agent_type": "deep_research", 
    "message": "Research the latest trends in AI",
    "agent_config": {"max_iterations": 100}
}
```

### Task Planner Agent (task_planner)
Task decomposition, progress tracking, resource allocation
```python
# Usage example
{
    "agent_type": "task_planner",
    "message": "Create a project development plan",
    "agent_config": {"timeout": 180}
}
```

## 🛠️ Tool System

### Built-in Tools

| Tool Name | Description | Category |
|-----------|-------------|----------|
| search | Web search | Search Tools |
| database_query | Database queries | Data Processing |
| calculator | Mathematical calculations | Utilities |
| file_operations | File handling | File Management |

### Custom Tools

```python
from app.tools.base import BaseTool

class MyCustomTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="my_custom_tool",
            description="My custom tool implementation"
        )
    
    async def execute(self, input_data):
        # Implement tool logic
        return {"result": "success"}
```

## 📖 Documentation

### Design Documents
- [📋 Super-V5 Design Document v2.0](Super-V5设计文档_v2.0.md) - Complete system design
- [🔧 Tool System Design](工具系统详细设计.md) - Tool system architecture
- [🗄️ Database Design](数据库设计详细文档.md) - Database design and optimization
- [🚀 Deployment Guide](部署运维详细文档.md) - Deployment and operations guide

### API Documentation
- [Swagger UI](http://localhost:8000/docs) - Interactive API documentation
- [ReDoc](http://localhost:8000/redoc) - Beautiful API documentation

### Development Guide
```bash
# Generate API documentation
python scripts/generate_docs.py

# Run tests
pytest tests/ -v

# Code formatting
black app/ tests/
isort app/ tests/

# Type checking
mypy app/

# Security scanning
bandit -r app/
```

## 🚀 Deployment

### Development Environment
```bash
# Start development services
docker-compose -f docker-compose.dev.yml up -d
```

### Testing Environment
```bash
# Deploy to staging environment
./scripts/deploy.sh --environment staging --image-tag develop
```

### Production Environment
```bash
# Kubernetes deployment
kubectl apply -f k8s/
./scripts/deploy.sh --environment production --image-tag v2.0.0

# Health check
./scripts/ops.sh health

# Monitoring check
open http://grafana.super-v5.com
```

## 📊 Monitoring Dashboard

### Application Monitoring
- **Grafana Dashboard**: http://grafana.super-v5.com
- **Prometheus Metrics**: http://prometheus.super-v5.com
- **Health Check**: http://api.super-v5.com/api/v1/health

### Key Metrics
- **QPS**: Queries per second
- **Response Time**: P95 response time < 2s
- **Error Rate**: Error rate < 1%
- **Availability**: Uptime > 99.9%

## 🔒 Security

### Authentication & Authorization
- JWT Token authentication
- RBAC permission control
- OAuth2 integration support

### Security Measures
- Input validation and sanitization
- SQL injection protection
- XSS attack protection
- Request rate limiting
- Data encryption

### Security Configuration
```python
# JWT configuration
JWT_SECRET_KEY = "your-secret-key"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_MINUTES = 30

# Rate limiting configuration
RATE_LIMIT_REQUESTS = 100
RATE_LIMIT_WINDOW = 60
```

## 🧪 Testing

### Run Tests
```bash
# Unit tests
pytest tests/unit/ -v

# Integration tests
pytest tests/integration/ -v

# End-to-end tests
pytest tests/e2e/ -v

# Coverage testing
pytest --cov=app --cov-report=html
```

### Performance Testing
```bash
# Load testing
./scripts/ops.sh load-test http://localhost:8000 10 1000

# Stress testing
k6 run tests/performance/load-test.js
```

## 🤝 Contributing

We welcome all forms of contributions!

### Contribution Process
1. Fork the project
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push branch (`git push origin feature/amazing-feature`)
5. Create Pull Request

### Development Standards
- Follow [PEP 8](https://pep8.org/) code standards
- Write unit tests
- Update documentation
- Pass CI/CD checks

### Code Quality
```bash
# Code checking
pre-commit run --all-files

# Security scanning
safety check

# Dependency checking
pip-audit
```

## 📄 License

This project is licensed under the [MIT License](LICENSE) - see the LICENSE file for details.

## 🙏 Acknowledgments

Thanks to the following open source projects and contributors:

- [FastAPI](https://fastapi.tiangolo.com/) - High-performance web framework
- [LangChain](https://python.langchain.com/) - LLM application development framework
- [LangGraph](https://python.langchain.com/docs/langgraph) - State machine workflow framework
- [Pydantic](https://pydantic-docs.helpmanual.io/) - Data validation library

## 📞 Contact Us

- **Project Homepage**: https://github.com/super-v5/super-v5
- **Issue Reporting**: https://github.com/super-v5/super-v5/issues
- **Feature Requests**: https://github.com/super-v5/super-v5/discussions
- **Email**: <EMAIL>

---

<div align="center">

**⭐ If this project helps you, please give us a Star!**

Made with ❤️ by Super-V5 Team

</div>
